/**
 * MyLunaChat Setup Plugin JavaScript
 */

(function($) {
    'use strict';

    let currentStep = 1;
    const totalSteps = 4;

    $(document).ready(function() {
        initSetup();
    });

    function initSetup() {
        // Initialize event handlers
        $('#install-plugins').on('click', installPlugins);
        $('#create-pages').on('click', createPages);
        $('#configure-settings').on('click', configureSettings);
        
        // Show first step
        showStep(1);
    }

    function updateProgress(step, text) {
        const percentage = (step / totalSteps) * 100;
        $('.progress-fill').css('width', percentage + '%');
        $('.progress-text').text(text || 'Step ' + step + ' of ' + totalSteps);
    }

    function showStep(step) {
        $('.setup-step').hide().removeClass('active');
        $('#step-' + getStepName(step)).show().addClass('active');
        currentStep = step;
        updateProgress(step);
    }

    function getStepName(step) {
        const steps = ['', 'plugins', 'pages', 'settings', 'complete'];
        return steps[step];
    }

    function installPlugins() {
        const button = $('#install-plugins');
        button.prop('disabled', true).html('<span class="loading-spinner"></span>Installing...');
        
        updateProgress(1, 'Installing plugins...');
        
        const plugins = $('.plugin-item');
        let completed = 0;
        
        plugins.each(function(index) {
            const pluginItem = $(this);
            const pluginKey = pluginItem.data('plugin');
            
            setTimeout(function() {
                installSinglePlugin(pluginKey, pluginItem, function() {
                    completed++;
                    if (completed === plugins.length) {
                        button.prop('disabled', false).html('All Plugins Installed ✓');
                        setTimeout(function() {
                            showStep(2);
                        }, 1000);
                    }
                });
            }, index * 500); // Stagger installations
        });
    }

    function installSinglePlugin(pluginKey, pluginItem, callback) {
        pluginItem.addClass('installing');
        pluginItem.find('.plugin-status').removeClass('pending').addClass('installing').text('Installing...');
        pluginItem.find('.plugin-progress').addClass('active');
        
        $.ajax({
            url: mylunachat_setup.ajax_url,
            type: 'POST',
            data: {
                action: 'mylunachat_install_plugin',
                plugin: pluginKey,
                nonce: mylunachat_setup.nonce
            },
            success: function(response) {
                if (response.success) {
                    pluginItem.removeClass('installing').addClass('completed');
                    pluginItem.find('.plugin-status').removeClass('installing').addClass('completed').text('Installed ✓');
                    pluginItem.find('.plugin-progress').removeClass('active');
                } else {
                    pluginItem.removeClass('installing').addClass('error');
                    pluginItem.find('.plugin-status').removeClass('installing').addClass('error').text('Error ✗');
                    pluginItem.find('.plugin-progress').removeClass('active');
                }
                callback();
            },
            error: function() {
                pluginItem.removeClass('installing').addClass('error');
                pluginItem.find('.plugin-status').removeClass('installing').addClass('error').text('Error ✗');
                pluginItem.find('.plugin-progress').removeClass('active');
                callback();
            }
        });
    }

    function createPages() {
        const button = $('#create-pages');
        button.prop('disabled', true).html('<span class="loading-spinner"></span>Creating Pages...');
        
        updateProgress(2, 'Creating site pages...');
        
        const pageItems = $('.page-item');
        
        // Animate page creation
        pageItems.each(function(index) {
            const pageItem = $(this);
            setTimeout(function() {
                pageItem.addClass('creating');
                setTimeout(function() {
                    pageItem.removeClass('creating').addClass('completed');
                }, 800);
            }, index * 200);
        });
        
        $.ajax({
            url: mylunachat_setup.ajax_url,
            type: 'POST',
            data: {
                action: 'mylunachat_create_pages',
                nonce: mylunachat_setup.nonce
            },
            success: function(response) {
                if (response.success) {
                    button.prop('disabled', false).html('All Pages Created ✓');
                    setTimeout(function() {
                        showStep(3);
                    }, 2000);
                } else {
                    button.prop('disabled', false).html('Error Creating Pages ✗');
                    showError('Failed to create pages: ' + (response.data.message || 'Unknown error'));
                }
            },
            error: function() {
                button.prop('disabled', false).html('Error Creating Pages ✗');
                showError('Failed to create pages due to connection error');
            }
        });
    }

    function configureSettings() {
        const button = $('#configure-settings');
        button.prop('disabled', true).html('<span class="loading-spinner"></span>Configuring...');
        
        updateProgress(3, 'Configuring settings...');
        
        const settingItems = $('.setting-item');
        
        // Animate settings configuration
        settingItems.each(function(index) {
            const settingItem = $(this);
            setTimeout(function() {
                settingItem.addClass('configuring');
                setTimeout(function() {
                    settingItem.removeClass('configuring').addClass('completed');
                }, 1000);
            }, index * 300);
        });
        
        $.ajax({
            url: mylunachat_setup.ajax_url,
            type: 'POST',
            data: {
                action: 'mylunachat_configure_settings',
                nonce: mylunachat_setup.nonce
            },
            success: function(response) {
                if (response.success) {
                    button.prop('disabled', false).html('Settings Configured ✓');
                    setTimeout(function() {
                        showStep(4);
                        updateProgress(4, 'Setup Complete! 🎉');
                        celebrateCompletion();
                    }, 3000);
                } else {
                    button.prop('disabled', false).html('Error Configuring ✗');
                    showError('Failed to configure settings: ' + (response.data.message || 'Unknown error'));
                }
            },
            error: function() {
                button.prop('disabled', false).html('Error Configuring ✗');
                showError('Failed to configure settings due to connection error');
            }
        });
    }

    function celebrateCompletion() {
        // Add some celebration effects
        createConfetti();
        
        // Play a subtle success sound (if available)
        if (typeof Audio !== 'undefined') {
            try {
                const successSound = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                successSound.volume = 0.3;
                successSound.play().catch(() => {}); // Ignore errors
            } catch (e) {
                // Ignore audio errors
            }
        }
    }

    function createConfetti() {
        const colors = ['#FFD700', '#30D5C8', '#D6BBFB', '#FF6B6B', '#4ECDC4'];
        const confettiCount = 50;
        
        for (let i = 0; i < confettiCount; i++) {
            setTimeout(function() {
                const confetti = $('<div class="confetti"></div>');
                confetti.css({
                    position: 'fixed',
                    left: Math.random() * 100 + '%',
                    top: '-10px',
                    width: '10px',
                    height: '10px',
                    backgroundColor: colors[Math.floor(Math.random() * colors.length)],
                    zIndex: 9999,
                    borderRadius: Math.random() > 0.5 ? '50%' : '0',
                    transform: 'rotate(' + (Math.random() * 360) + 'deg)'
                });
                
                $('body').append(confetti);
                
                confetti.animate({
                    top: $(window).height() + 'px',
                    left: '+=' + (Math.random() * 200 - 100) + 'px'
                }, Math.random() * 3000 + 2000, function() {
                    confetti.remove();
                });
            }, i * 100);
        }
    }

    function showError(message) {
        const errorDiv = $('<div class="setup-error"></div>');
        errorDiv.html('<strong>Error:</strong> ' + message);
        errorDiv.css({
            background: '#f8d7da',
            color: '#721c24',
            padding: '15px',
            borderRadius: '8px',
            margin: '20px 0',
            border: '1px solid #f5c6cb'
        });
        
        $('.setup-step.active').prepend(errorDiv);
        
        setTimeout(function() {
            errorDiv.fadeOut(function() {
                errorDiv.remove();
            });
        }, 5000);
    }

    // Utility functions
    function showNotification(message, type) {
        type = type || 'info';
        const notification = $('<div class="setup-notification"></div>');
        notification.html(message);
        notification.addClass('notification-' + type);
        
        const colors = {
            success: { bg: '#d4edda', color: '#155724', border: '#c3e6cb' },
            error: { bg: '#f8d7da', color: '#721c24', border: '#f5c6cb' },
            warning: { bg: '#fff3cd', color: '#856404', border: '#ffeaa7' },
            info: { bg: '#d1ecf1', color: '#0c5460', border: '#bee5eb' }
        };
        
        notification.css({
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '15px 20px',
            borderRadius: '8px',
            zIndex: 10000,
            maxWidth: '400px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            background: colors[type].bg,
            color: colors[type].color,
            border: '1px solid ' + colors[type].border,
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease'
        });
        
        $('body').append(notification);
        
        setTimeout(function() {
            notification.css('transform', 'translateX(0)');
        }, 100);
        
        setTimeout(function() {
            notification.css('transform', 'translateX(100%)');
            setTimeout(function() {
                notification.remove();
            }, 300);
        }, 4000);
    }

    // Keyboard shortcuts
    $(document).on('keydown', function(e) {
        if (e.ctrlKey || e.metaKey) {
            switch (e.which) {
                case 49: // Ctrl+1
                    e.preventDefault();
                    showStep(1);
                    break;
                case 50: // Ctrl+2
                    e.preventDefault();
                    showStep(2);
                    break;
                case 51: // Ctrl+3
                    e.preventDefault();
                    showStep(3);
                    break;
                case 52: // Ctrl+4
                    e.preventDefault();
                    showStep(4);
                    break;
            }
        }
    });

    // Add tooltips for better UX
    $('[data-tooltip]').hover(
        function() {
            const tooltip = $('<div class="setup-tooltip"></div>');
            tooltip.text($(this).data('tooltip'));
            tooltip.css({
                position: 'absolute',
                background: '#333',
                color: '#fff',
                padding: '8px 12px',
                borderRadius: '4px',
                fontSize: '12px',
                zIndex: 10001,
                whiteSpace: 'nowrap',
                top: $(this).offset().top - 35,
                left: $(this).offset().left + ($(this).width() / 2) - (tooltip.width() / 2)
            });
            $('body').append(tooltip);
            tooltip.fadeIn(200);
        },
        function() {
            $('.setup-tooltip').fadeOut(200, function() {
                $(this).remove();
            });
        }
    );

})(jQuery);
