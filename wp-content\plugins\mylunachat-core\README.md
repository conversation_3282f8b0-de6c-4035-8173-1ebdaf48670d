# 🌙 MyLunaChat Core Plugin Documentation

This plugin provides modular, reusable PHP functions and shortcodes for the MyLunaChat WordPress platform.

## 🔹 1. AI Usage Enforcement Functions

### `mylunachat_can_use_ai($user_id)`
Returns `true` if the user is premium OR has used less than 3 free AI interactions today.

```php
// Check if current user can use AI
if (mylunachat_can_use_ai()) {
    // User can make AI request
    echo "You can use AI features!";
} else {
    // Show upgrade prompt
    echo "Daily limit reached. Please upgrade to Premium.";
}

// Check for specific user
if (mylunachat_can_use_ai(123)) {
    // User ID 123 can use AI
}
```

### `mylunachat_get_daily_ai_usage($user_id)`
Returns the count of AI interactions for today.

```php
$usage = mylunachat_get_daily_ai_usage();
echo "You've used {$usage} AI consultations today.";

// For specific user
$user_usage = mylunachat_get_daily_ai_usage(123);
```

### `mylunachat_increment_ai_usage($user_id)`
Increments daily usage count. Automatically resets daily using date-based keys.

```php
// Increment usage for current user
mylunachat_increment_ai_usage();

// Increment for specific user
mylunachat_increment_ai_usage(123);
```

### `mylunachat_get_remaining_ai_uses($user_id)`
Returns remaining uses for today or "unlimited" for premium users.

```php
$remaining = mylunachat_get_remaining_ai_uses();
if ($remaining === 'unlimited') {
    echo "Premium user - unlimited access!";
} else {
    echo "You have {$remaining} consultations remaining today.";
}
```

## 🔹 2. Rewarded Ad Shortcode

### Basic Usage
```php
[mylunachat_rewarded_ai feature="dream_interpreter" delay="15"]
```

### Parameters
- `feature`: The feature to unlock (dream_interpreter, birth_chart, compatibility, ai_chat, horoscope)
- `delay`: Seconds to wait before showing continue button (default: 15)
- `ad_client`: AdSense client ID (optional, uses site setting)
- `ad_slot`: AdSense slot ID (optional, uses site setting)
- `premium_message`: Custom message for premium users

### Advanced Examples
```php
// Dream interpreter with 20-second delay
[mylunachat_rewarded_ai feature="dream_interpreter" delay="20"]

// Birth chart with custom AdSense settings
[mylunachat_rewarded_ai feature="birth_chart" delay="15" ad_client="ca-pub-1234567890" ad_slot="9876543210"]

// Compatibility reading with custom premium message
[mylunachat_rewarded_ai feature="compatibility" delay="10" premium_message="Welcome back, Cosmic VIP! 🌟"]
```

### Behavior
- **Premium users**: Skip ad entirely, show feature immediately with premium welcome message
- **Free users**: Show AdSense ad, countdown timer, then continue button after delay
- **After continue**: Hide ad, load feature content via AJAX

## 🔹 3. AI Wrapper Class

### Basic Usage
```php
// Get AI response with context
$response = MylunaChatAI::get_response(
    "What does my birth chart say about my career?",
    "birth_chart",
    get_current_user_id()
);

if ($response['success']) {
    echo $response['data']; // AI response text
    echo "Remaining uses: " . $response['remaining_uses'];
} else {
    echo "Error: " . $response['message'];
}
```

### Available Contexts
- `birth_chart`: Natal chart interpretation
- `dream_interpreter`: Dream analysis with Jungian approach
- `compatibility`: Relationship astrology
- `horoscope`: Daily/weekly/monthly horoscopes
- `general`: General astrological guidance

### Response Format
```php
array(
    'success' => true/false,
    'data' => 'AI response text', // if success
    'error' => 'error_code', // if failure
    'message' => 'Human readable message',
    'remaining_uses' => 2 // or 'unlimited'
)
```

### Error Codes
- `daily_limit_reached`: User has exceeded free daily limit
- `api_not_configured`: OpenAI API key not set
- `api_error`: Connection to OpenAI failed
- `invalid_response`: Malformed API response

### Conversation History
```php
// Get user's recent conversations
$history = MylunaChatAI::get_conversation_history($user_id, 10);

foreach ($history as $conversation) {
    echo "User: " . $conversation['prompt'] . "\n";
    echo "AI: " . $conversation['response'] . "\n";
    echo "Context: " . $conversation['context'] . "\n";
    echo "Date: " . $conversation['timestamp'] . "\n\n";
}
```

## 🔹 4. Usage Widget Shortcode

### Basic Usage
```php
[mylunachat_usage_widget]
```

### With Options
```php
[mylunachat_usage_widget show_upgrade="false"]
```

### Display
- **Premium users**: Shows premium badge and "Unlimited AI consultations"
- **Free users**: Shows usage bar, remaining consultations, upgrade prompt

## 🔹 5. Utility Functions

### `mylunachat_get_usage_stats($user_id)`
Returns comprehensive usage statistics.

```php
$stats = mylunachat_get_usage_stats();
/*
Returns:
array(
    'is_premium' => false,
    'daily_usage' => 2,
    'remaining_uses' => 1,
    'free_limit' => 3,
    'percentage_used' => 66.67
)
*/
```

### `mylunachat_is_premium_user($user_id)`
Checks if user has active premium membership.

```php
if (mylunachat_is_premium_user()) {
    // Show premium features
}
```

## 🔹 6. JavaScript API

### Global Functions
```javascript
// Show feature content (used by rewarded ads)
mylunachatShowFeature('instance_id', 'dream_interpreter');

// Check if user can use AI
if (MyLunaChatCore.canUseAI()) {
    // Proceed with AI request
}

// Show upgrade modal
MyLunaChatCore.showUpgrade();

// Update usage widget
MyLunaChatCore.updateUsage(2); // 2 remaining uses
```

### Events
```javascript
// Listen for feature loaded
$(document).on('mylunachat:feature_loaded', function(event, data) {
    console.log('Feature loaded:', data.feature);
    console.log('Remaining uses:', data.remaining_uses);
});

// Listen for AI response
$(document).on('mylunachat:ai_response', function(event, data) {
    console.log('AI responded:', data.message);
});

// Listen for usage updates
$(document).on('mylunachat:usage_updated', function(event, data) {
    console.log('Usage updated:', data.remaining_uses);
});
```

## 🔹 7. Admin Configuration

### Settings Page
Navigate to **WordPress Admin → MyLunaChat Setup → Core Settings**

### Available Settings
- **AdSense Client ID**: Your Google AdSense publisher ID
- **AdSense Ad Slot**: Ad unit slot ID for rewarded ads
- **Usage Statistics**: View platform usage metrics

### Programmatic Configuration
```php
// Set AdSense settings
update_option('mylunachat_adsense_client', 'ca-pub-1234567890');
update_option('mylunachat_adsense_slot', '9876543210');

// Set AI limits (already defined in wp-config.php)
// MYLUNACHAT_FREE_AI_LIMIT = 3
// MYLUNACHAT_PREMIUM_PRICE = 10.00
```

## 🔹 8. Integration Examples

### Custom Page Template
```php
<?php
// Check if user can access premium content
if (!mylunachat_is_premium_user() && !mylunachat_can_use_ai()) {
    // Show upgrade prompt
    echo do_shortcode('[mylunachat_usage_widget]');
    return;
}

// Show rewarded ad for dream interpretation
echo do_shortcode('[mylunachat_rewarded_ai feature="dream_interpreter" delay="15"]');
?>
```

### Custom AI Integration
```php
function my_custom_ai_feature() {
    $user_input = sanitize_text_field($_POST['user_question']);
    
    $response = MylunaChatAI::get_response(
        $user_input,
        'general',
        get_current_user_id()
    );
    
    if ($response['success']) {
        wp_send_json_success(array(
            'answer' => $response['data'],
            'remaining' => $response['remaining_uses']
        ));
    } else {
        wp_send_json_error($response['message']);
    }
}
add_action('wp_ajax_my_custom_ai_feature', 'my_custom_ai_feature');
```

## 🔹 9. Hooks & Filters

### Available Hooks
```php
// Before AI response (modify prompt)
add_filter('mylunachat_ai_prompt', function($prompt, $context, $user_id) {
    // Modify prompt based on user or context
    return $prompt;
}, 10, 3);

// After AI response (modify response)
add_filter('mylunachat_ai_response', function($response, $context, $user_id) {
    // Modify AI response
    return $response;
}, 10, 3);

// Usage limit check
add_filter('mylunachat_usage_limit', function($limit, $user_id) {
    // Custom usage limits
    return $limit;
}, 10, 2);
```

## 🔹 10. Troubleshooting

### Common Issues

**AI not working**: Check OpenAI API key in MyLunaChat AI Settings
**Ads not showing**: Verify AdSense client ID and slot ID in Core Settings
**Usage not tracking**: Ensure user is logged in and database is writable
**Premium not recognized**: Check MemberPress integration

### Debug Mode
```php
// Enable debug logging
define('MYLUNACHAT_DEBUG', true);

// Check logs in wp-content/debug.log
```
