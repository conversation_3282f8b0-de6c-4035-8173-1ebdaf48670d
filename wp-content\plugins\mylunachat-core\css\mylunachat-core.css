/**
 * MyLunaChat Core Plugin Styles
 */

/* ========================================
   REWARDED AD CONTAINER
   ======================================== */

.mylunachat-rewarded-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: linear-gradient(135deg, rgba(10, 15, 41, 0.95), rgba(26, 31, 58, 0.95));
    border-radius: 15px;
    border: 1px solid rgba(214, 187, 251, 0.2);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    color: #F5F5F5;
}

/* Premium Welcome Section */
.mylunachat-premium-welcome {
    text-align: center;
    margin-bottom: 30px;
}

.premium-badge {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #0A0F29;
    padding: 15px 25px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 1.1em;
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
}

.premium-icon {
    font-size: 1.2em;
}

/* Ad Container */
.mylunachat-ad-container {
    text-align: center;
    padding: 30px;
    background: rgba(245, 245, 245, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(48, 213, 200, 0.2);
}

.ad-header h3 {
    color: #FFD700;
    margin-bottom: 10px;
    font-size: 1.5em;
}

.ad-header p {
    color: #D6BBFB;
    margin-bottom: 20px;
}

.ad-timer {
    background: rgba(48, 213, 200, 0.1);
    border: 1px solid #30D5C8;
    border-radius: 20px;
    padding: 10px 20px;
    margin: 20px auto;
    display: inline-block;
    font-weight: 600;
    color: #30D5C8;
}

.ready-indicator {
    color: #28a745;
    font-weight: 600;
}

/* AdSense Container */
.adsense-container {
    margin: 30px 0;
    min-height: 250px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 20px;
    border: 1px dashed rgba(214, 187, 251, 0.3);
}

/* Continue Section */
.continue-section {
    margin-top: 30px;
}

.mylunachat-continue-btn {
    background: linear-gradient(135deg, #30D5C8, #D6BBFB);
    color: #0A0F29;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 1.1em;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mylunachat-continue-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(48, 213, 200, 0.4);
}

/* Feature Content */
.mylunachat-feature-content {
    margin-top: 30px;
    padding: 30px;
    background: rgba(245, 245, 245, 0.03);
    border-radius: 12px;
    border: 1px solid rgba(214, 187, 251, 0.1);
}

.loading-message {
    text-align: center;
    padding: 40px;
}

.cosmic-loader {
    font-size: 2em;
    margin-bottom: 20px;
    animation: cosmic-pulse 2s infinite;
}

@keyframes cosmic-pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
}

/* ========================================
   USAGE WIDGET
   ======================================== */

.mylunachat-usage-widget {
    background: linear-gradient(135deg, rgba(10, 15, 41, 0.9), rgba(26, 31, 58, 0.9));
    border: 1px solid rgba(214, 187, 251, 0.2);
    border-radius: 12px;
    padding: 20px;
    color: #F5F5F5;
    max-width: 400px;
    margin: 20px auto;
}

.usage-premium {
    text-align: center;
}

.usage-premium .premium-badge {
    margin-bottom: 15px;
}

.usage-free .usage-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.usage-header h4 {
    margin: 0;
    color: #FFD700;
}

.usage-count {
    background: rgba(48, 213, 200, 0.2);
    color: #30D5C8;
    padding: 5px 12px;
    border-radius: 15px;
    font-weight: 600;
    font-size: 0.9em;
}

/* Usage Progress Bar */
.usage-bar {
    width: 100%;
    height: 8px;
    background: rgba(245, 245, 245, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 15px;
}

.usage-progress {
    height: 100%;
    background: linear-gradient(90deg, #30D5C8, #D6BBFB);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.usage-remaining {
    font-size: 0.9em;
    color: #D6BBFB;
    margin-bottom: 15px;
    text-align: center;
}

.upgrade-prompt {
    text-align: center;
}

.upgrade-prompt .btn-premium {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #0A0F29;
    padding: 10px 20px;
    border-radius: 20px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9em;
    display: inline-block;
    transition: all 0.3s ease;
}

.upgrade-prompt .btn-premium:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
}

/* ========================================
   FORM STYLES
   ======================================== */

.mylunachat-form {
    background: rgba(245, 245, 245, 0.05);
    padding: 30px;
    border-radius: 12px;
    border: 1px solid rgba(214, 187, 251, 0.2);
    margin: 20px 0;
}

.mylunachat-form .form-group {
    margin-bottom: 20px;
}

.mylunachat-form label {
    display: block;
    color: #FFD700;
    font-weight: 600;
    margin-bottom: 8px;
}

.mylunachat-form input,
.mylunachat-form select,
.mylunachat-form textarea {
    width: 100%;
    padding: 12px 15px;
    background: rgba(245, 245, 245, 0.1);
    border: 1px solid rgba(214, 187, 251, 0.3);
    border-radius: 8px;
    color: #F5F5F5;
    font-size: 1em;
    transition: all 0.3s ease;
}

.mylunachat-form input:focus,
.mylunachat-form select:focus,
.mylunachat-form textarea:focus {
    outline: none;
    border-color: #30D5C8;
    box-shadow: 0 0 10px rgba(48, 213, 200, 0.3);
}

.mylunachat-form input::placeholder,
.mylunachat-form textarea::placeholder {
    color: rgba(245, 245, 245, 0.6);
}

.mylunachat-form .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

/* ========================================
   RESPONSIVE DESIGN
   ======================================== */

@media (max-width: 768px) {
    .mylunachat-rewarded-container {
        margin: 10px;
        padding: 15px;
    }
    
    .mylunachat-ad-container {
        padding: 20px;
    }
    
    .ad-header h3 {
        font-size: 1.3em;
    }
    
    .premium-badge {
        padding: 12px 20px;
        font-size: 1em;
    }
    
    .mylunachat-continue-btn {
        padding: 12px 25px;
        font-size: 1em;
    }
    
    .mylunachat-form .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .mylunachat-usage-widget {
        margin: 15px;
        padding: 15px;
    }
}

/* ========================================
   ADMIN STYLES
   ======================================== */

.mylunachat-admin-stats {
    background: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #ddd;
    margin-top: 20px;
}

.mylunachat-admin-stats p {
    margin: 10px 0;
    font-size: 1.1em;
}

/* ========================================
   ANIMATIONS
   ======================================== */

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.mylunachat-feature-content {
    animation: fadeInUp 0.5s ease-out;
}

/* ========================================
   UTILITY CLASSES
   ======================================== */

.text-center {
    text-align: center;
}

.mb-20 {
    margin-bottom: 20px;
}

.mt-20 {
    margin-top: 20px;
}

.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

/* ========================================
   EMAIL MODAL STYLES
   ======================================== */

.mylunachat-email-section {
    margin-top: 20px;
    text-align: center;
    padding: 15px;
    background: rgba(245, 245, 245, 0.03);
    border-radius: 8px;
    border-top: 1px solid rgba(214, 187, 251, 0.2);
}

.mylunachat-email-btn {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #0A0F29;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9em;
}

.mylunachat-email-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
}

.mylunachat-email-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.mylunachat-email-modal {
    background: linear-gradient(135deg, #0A0F29, #1a1f3a);
    border-radius: 15px;
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(214, 187, 251, 0.2);
}

.mylunachat-email-modal .modal-header {
    padding: 25px 30px 15px;
    border-bottom: 1px solid rgba(214, 187, 251, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.mylunachat-email-modal .modal-header h3 {
    margin: 0;
    color: #FFD700;
    font-size: 1.4em;
}

.mylunachat-email-modal .modal-close {
    background: none;
    border: none;
    color: #D6BBFB;
    font-size: 1.5em;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.mylunachat-email-modal .modal-close:hover {
    background: rgba(214, 187, 251, 0.1);
    color: #FFD700;
}

.mylunachat-email-modal .modal-content {
    padding: 25px 30px 30px;
}

.mylunachat-email-modal .form-group {
    margin-bottom: 20px;
}

.mylunachat-email-modal label {
    display: block;
    color: #D6BBFB;
    font-weight: 600;
    margin-bottom: 8px;
}

.mylunachat-email-modal .form-control {
    width: 100%;
    padding: 12px 15px;
    background: rgba(245, 245, 245, 0.1);
    border: 1px solid rgba(214, 187, 251, 0.3);
    border-radius: 8px;
    color: #F5F5F5;
    font-size: 1em;
    transition: all 0.3s ease;
}

.mylunachat-email-modal .form-control:focus {
    outline: none;
    border-color: #30D5C8;
    box-shadow: 0 0 10px rgba(48, 213, 200, 0.3);
}

.mylunachat-email-modal .form-control::placeholder {
    color: rgba(245, 245, 245, 0.6);
}

/* Checkbox styling */
.checkbox-label {
    display: flex !important;
    align-items: flex-start;
    cursor: pointer;
    font-weight: normal !important;
    font-size: 0.9em;
    line-height: 1.4;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: rgba(245, 245, 245, 0.1);
    border: 2px solid rgba(214, 187, 251, 0.3);
    border-radius: 4px;
    margin-right: 12px;
    margin-top: 2px;
    flex-shrink: 0;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #30D5C8;
    border-color: #30D5C8;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #0A0F29;
    font-weight: bold;
    font-size: 12px;
}

.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 25px;
}

.modal-actions .btn {
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    font-size: 1em;
}

.modal-actions .btn-primary {
    background: linear-gradient(135deg, #30D5C8, #D6BBFB);
    color: #0A0F29;
}

.modal-actions .btn-secondary {
    background: rgba(245, 245, 245, 0.1);
    color: #D6BBFB;
    border: 1px solid rgba(214, 187, 251, 0.3);
}

.modal-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Email success/error states */
.email-success {
    text-align: center;
    padding: 30px 20px;
}

.email-success .success-icon {
    font-size: 3em;
    margin-bottom: 15px;
}

.email-success h3 {
    color: #28a745;
    margin-bottom: 15px;
}

.email-success p {
    color: #D6BBFB;
    margin-bottom: 25px;
}

.email-error {
    margin-bottom: 20px;
}

.email-error .error-message {
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.3);
    border-radius: 8px;
    padding: 12px 15px;
    color: #f8d7da;
    display: flex;
    align-items: center;
    gap: 10px;
}

.email-error .error-icon {
    font-size: 1.2em;
}

/* Responsive email modal */
@media (max-width: 768px) {
    .mylunachat-email-modal {
        margin: 10px;
        max-width: none;
    }

    .mylunachat-email-modal .modal-header,
    .mylunachat-email-modal .modal-content {
        padding: 20px;
    }

    .modal-actions {
        flex-direction: column;
    }

    .modal-actions .btn {
        width: 100%;
    }
}
