/*
Theme Name: MyLunaChat Child Theme
Description: Custom child theme for MyLunaChat.com Astrology Platform
Author: MyLunaChat Development Team
Template: astra
Version: 1.0.0
*/

/* Import parent theme styles */
@import url("../astra/style.css");

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Cinzel+Decorative:wght@400;700&family=Playfair+Display:wght@400;500;600;700&family=Lato:wght@300;400;500;700&family=Open+Sans:wght@300;400;500;600;700&display=swap');

/* MyLunaChat Design System */
:root {
    /* Brand Colors */
    --mylunachat-navy: #0A0F29;
    --mylunachat-white: #F5F5F5;
    --mylunachat-gold: #FFD700;
    --mylunachat-lavender: #D6BBFB;
    --mylunachat-teal: #30D5C8;
    
    /* Typography */
    --font-heading: 'Cinzel Decorative', serif;
    --font-heading-alt: 'Playfair Display', serif;
    --font-body: 'Lato', sans-serif;
    --font-body-alt: 'Open Sans', sans-serif;
    
    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 20px;
}

/* Global Styles */
body {
    background-color: var(--mylunachat-navy);
    color: var(--mylunachat-white);
    font-family: var(--font-body);
    line-height: 1.6;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    color: var(--mylunachat-gold);
    margin-bottom: var(--spacing-md);
}

h1 {
    font-size: 2.5rem;
    font-weight: 700;
}

h2 {
    font-size: 2rem;
    font-weight: 400;
}

h3 {
    font-size: 1.5rem;
    font-weight: 400;
}

/* Alternative heading style */
.heading-alt {
    font-family: var(--font-heading-alt);
}

/* Links */
a {
    color: var(--mylunachat-teal);
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: var(--mylunachat-gold);
    text-decoration: underline;
}

/* Buttons */
.btn, .button, input[type="submit"], button {
    background: linear-gradient(135deg, var(--mylunachat-teal), var(--mylunachat-lavender));
    color: var(--mylunachat-navy);
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    font-family: var(--font-body);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn:hover, .button:hover, input[type="submit"]:hover, button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(48, 213, 200, 0.3);
    color: var(--mylunachat-navy);
}

/* Premium Button */
.btn-premium {
    background: linear-gradient(135deg, var(--mylunachat-gold), #FFA500);
    color: var(--mylunachat-navy);
    font-weight: 600;
}

.btn-premium:hover {
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
}

/* Cards */
.card {
    background: rgba(245, 245, 245, 0.05);
    border: 1px solid rgba(214, 187, 251, 0.2);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    border-color: var(--starpath-teal);
    box-shadow: 0 10px 30px rgba(48, 213, 200, 0.2);
}

/* Zodiac Signs */
.zodiac-symbol {
    color: var(--mylunachat-lavender);
    font-size: 2rem;
    margin-right: var(--spacing-sm);
}

/* Celestial Background Elements */
.celestial-bg {
    position: relative;
    overflow: hidden;
}

.celestial-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(2px 2px at 20px 30px, var(--mylunachat-gold), transparent),
        radial-gradient(2px 2px at 40px 70px, var(--mylunachat-lavender), transparent),
        radial-gradient(1px 1px at 90px 40px, var(--mylunachat-teal), transparent),
        radial-gradient(1px 1px at 130px 80px, var(--mylunachat-gold), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    opacity: 0.3;
    z-index: -1;
}

/* Navigation */
.main-navigation {
    background: rgba(10, 15, 41, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(214, 187, 251, 0.2);
}

.main-navigation a {
    color: var(--mylunachat-white);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.main-navigation a:hover {
    color: var(--mylunachat-gold);
}

/* Footer */
.site-footer {
    background: var(--mylunachat-navy);
    border-top: 1px solid rgba(214, 187, 251, 0.2);
    padding: var(--spacing-xl) 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    h1 {
        font-size: 2rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    .card {
        padding: var(--spacing-md);
    }
}

/* Premium Content Styling */
.premium-content {
    border: 2px solid var(--mylunachat-gold);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(214, 187, 251, 0.1));
    position: relative;
}

.premium-badge {
    position: absolute;
    top: -10px;
    right: var(--spacing-md);
    background: var(--mylunachat-gold);
    color: var(--mylunachat-navy);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

/* Loading Animation */
.loading-stars {
    display: inline-block;
    animation: twinkle 2s infinite;
}

@keyframes twinkle {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

/* AI Chat Interface */
.ai-chat-container {
    background: rgba(245, 245, 245, 0.05);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    max-height: 500px;
    overflow-y: auto;
}

.ai-message {
    background: rgba(48, 213, 200, 0.1);
    border-left: 3px solid var(--mylunachat-teal);
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    border-radius: var(--radius-sm);
}

.user-message {
    background: rgba(214, 187, 251, 0.1);
    border-left: 3px solid var(--mylunachat-lavender);
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    border-radius: var(--radius-sm);
    text-align: right;
}
