<?php
/**
 * The main template file for StarPath Child Theme
 *
 * This is the most generic template file in a WordPress theme
 * and one of the two required files for a theme (the other being style.css).
 * It is used to display a page when nothing more specific matches a query.
 */

get_header(); ?>

<div class="site-content celestial-bg">
    <div class="container">
        <main id="main" class="site-main">
            
            <?php if (have_posts()) : ?>
                
                <div class="posts-grid">
                    <?php while (have_posts()) : the_post(); ?>
                        
                        <article id="post-<?php the_ID(); ?>" <?php post_class('card fade-in'); ?>>
                            
                            <?php if (has_post_thumbnail()) : ?>
                                <div class="post-thumbnail">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php the_post_thumbnail('medium'); ?>
                                    </a>
                                </div>
                            <?php endif; ?>
                            
                            <div class="post-content">
                                <header class="entry-header">
                                    <?php
                                    if (is_singular()) :
                                        the_title('<h1 class="entry-title">', '</h1>');
                                    else :
                                        the_title('<h2 class="entry-title"><a href="' . esc_url(get_permalink()) . '" rel="bookmark">', '</a></h2>');
                                    endif;
                                    ?>
                                    
                                    <div class="entry-meta">
                                        <span class="posted-on">
                                            <time class="entry-date published" datetime="<?php echo esc_attr(get_the_date('c')); ?>">
                                                <?php echo get_the_date(); ?>
                                            </time>
                                        </span>
                                        <span class="byline">
                                            by <span class="author vcard">
                                                <a class="url fn n" href="<?php echo esc_url(get_author_posts_url(get_the_author_meta('ID'))); ?>">
                                                    <?php echo get_the_author(); ?>
                                                </a>
                                            </span>
                                        </span>
                                    </div>
                                </header>
                                
                                <div class="entry-summary">
                                    <?php the_excerpt(); ?>
                                </div>
                                
                                <footer class="entry-footer">
                                    <?php
                                    $categories_list = get_the_category_list(', ');
                                    if ($categories_list) {
                                        printf('<span class="cat-links">Categories: %1$s</span>', $categories_list);
                                    }
                                    
                                    $tags_list = get_the_tag_list('', ', ');
                                    if ($tags_list) {
                                        printf('<span class="tags-links">Tags: %1$s</span>', $tags_list);
                                    }
                                    ?>
                                </footer>
                            </div>
                            
                        </article>
                        
                    <?php endwhile; ?>
                </div>
                
                <nav class="navigation pagination">
                    <div class="nav-links">
                        <?php
                        echo paginate_links(array(
                            'prev_text' => '← Previous',
                            'next_text' => 'Next →',
                        ));
                        ?>
                    </div>
                </nav>
                
            <?php else : ?>
                
                <section class="no-results not-found card">
                    <header class="page-header">
                        <h1 class="page-title"><?php _e('Nothing here', 'starpath'); ?></h1>
                    </header>
                    
                    <div class="page-content">
                        <?php if (is_home() && current_user_can('publish_posts')) : ?>
                            <p><?php
                                printf(
                                    wp_kses(
                                        __('Ready to publish your first post? <a href="%1$s">Get started here</a>.', 'starpath'),
                                        array(
                                            'a' => array(
                                                'href' => array(),
                                            ),
                                        )
                                    ),
                                    esc_url(admin_url('post-new.php'))
                                );
                            ?></p>
                        <?php elseif (is_search()) : ?>
                            <p><?php _e('Sorry, but nothing matched your search terms. Please try again with some different keywords.', 'starpath'); ?></p>
                            <?php get_search_form(); ?>
                        <?php else : ?>
                            <p><?php _e('It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help.', 'starpath'); ?></p>
                            <?php get_search_form(); ?>
                        <?php endif; ?>
                    </div>
                </section>
                
            <?php endif; ?>
            
        </main>
    </div>
</div>

<?php
get_sidebar();
get_footer();
?>
