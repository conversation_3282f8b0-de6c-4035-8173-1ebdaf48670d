<?php
/**
 * Plugin Name: StarPath AI Integration
 * Plugin URI: https://starpath.com
 * Description: AI-powered astrology features for StarPath platform including birth chart analysis, dream interpretation, and personalized readings
 * Version: 1.0.0
 * Author: StarPath Development Team
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('STARPATH_AI_VERSION', '1.0.0');
define('STARPATH_AI_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('STARPATH_AI_PLUGIN_URL', plugin_dir_url(__FILE__));

/**
 * Main StarPath AI Class
 */
class StarPath_AI {
    
    private $ai_prompts = array();
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_ajax_starpath_ai_chat', array($this, 'handle_ai_chat'));
        add_action('wp_ajax_nopriv_starpath_ai_chat', array($this, 'handle_ai_chat'));
        add_action('wp_ajax_starpath_generate_birth_chart', array($this, 'handle_birth_chart'));
        add_action('wp_ajax_nopriv_starpath_generate_birth_chart', array($this, 'handle_birth_chart'));
        add_action('wp_ajax_starpath_compatibility_reading', array($this, 'handle_compatibility'));
        add_action('wp_ajax_nopriv_starpath_compatibility_reading', array($this, 'handle_compatibility'));
        add_action('wp_ajax_starpath_interpret_dream', array($this, 'handle_dream_interpretation'));
        add_action('wp_ajax_nopriv_starpath_interpret_dream', array($this, 'handle_dream_interpretation'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        $this->init_ai_prompts();
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Register shortcodes
        add_shortcode('starpath_ai_chat', array($this, 'ai_chat_shortcode'));
        add_shortcode('starpath_birth_chart', array($this, 'birth_chart_shortcode'));
        add_shortcode('starpath_compatibility', array($this, 'compatibility_shortcode'));
        add_shortcode('starpath_dream_interpreter', array($this, 'dream_interpreter_shortcode'));
        add_shortcode('starpath_horoscope', array($this, 'horoscope_shortcode'));
    }
    
    /**
     * Initialize AI prompts
     */
    private function init_ai_prompts() {
        $this->ai_prompts = array(
            'birth_chart' => "You are a professional astrologer with deep knowledge of natal chart interpretation. Analyze the birth chart based on the provided birth date, time, and location. Include insights about:\n\n1. Sun, Moon, and Rising signs\n2. Planetary positions and their meanings\n3. House placements and their significance\n4. Major aspects between planets\n5. Overall personality traits and life themes\n\nUse a warm, encouraging, and mystical tone. Provide practical guidance while maintaining the spiritual essence of astrology. Keep the reading comprehensive but accessible to beginners.",
            
            'ai_reading' => "You are an intuitive AI astrologer and spiritual guide. You have access to vast astrological knowledge and can provide personalized insights based on birth charts, current transits, and cosmic energies. Your responses should be:\n\n1. Warm and compassionate\n2. Spiritually insightful yet practical\n3. Encouraging and empowering\n4. Based on astrological principles\n5. Personalized to the user's question\n\nAlways ask for birth details if not provided, and relate your guidance to current planetary movements when relevant.",
            
            'dream_interpreter' => "You are a Jungian dream analyst and spiritual guide with expertise in dream symbolism, archetypes, and astrological connections. When interpreting dreams:\n\n1. Explore symbolic meanings and archetypes\n2. Consider emotional context and feelings\n3. Look for connections to the dreamer's waking life\n4. Include astrological symbolism when relevant\n5. Provide spiritual and psychological insights\n6. Offer practical guidance for integration\n\nUse a compassionate, insightful tone that honors the sacred nature of dreams. Help the dreamer understand both the personal and universal meanings.",
            
            'compatibility' => "You are an expert relationship astrologer specializing in synastry and compatibility analysis. When analyzing compatibility between two people:\n\n1. Compare sun, moon, and rising signs\n2. Analyze Venus and Mars placements for romantic compatibility\n3. Look at communication styles (Mercury)\n4. Consider emotional needs and expressions\n5. Identify potential challenges and strengths\n6. Provide practical relationship advice\n\nBe balanced in your assessment - highlight both harmonious aspects and potential growth areas. Use an encouraging tone that supports healthy relationships.",
            
            'horoscope' => "You are a skilled astrologer creating personalized horoscopes. Based on the zodiac sign and current planetary transits, provide:\n\n1. General energy and themes for the period\n2. Love and relationship insights\n3. Career and financial guidance\n4. Health and wellness advice\n5. Spiritual growth opportunities\n6. Lucky numbers, colors, or elements\n\nKeep the tone uplifting and empowering. Focus on opportunities for growth and positive manifestation while acknowledging challenges as learning experiences."
        );
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_submenu_page(
            'starpath-setup',
            'AI Configuration',
            'AI Settings',
            'manage_options',
            'starpath-ai-config',
            array($this, 'admin_page')
        );
    }
    
    /**
     * Admin configuration page
     */
    public function admin_page() {
        if (isset($_POST['save_settings'])) {
            update_option('starpath_ai_api_key', sanitize_text_field($_POST['ai_api_key']));
            update_option('starpath_ai_model', sanitize_text_field($_POST['ai_model']));
            update_option('starpath_ai_temperature', floatval($_POST['ai_temperature']));
            echo '<div class="notice notice-success"><p>Settings saved successfully!</p></div>';
        }
        
        $api_key = get_option('starpath_ai_api_key', '');
        $model = get_option('starpath_ai_model', 'gpt-3.5-turbo');
        $temperature = get_option('starpath_ai_temperature', 0.7);
        ?>
        <div class="wrap">
            <h1>🤖 StarPath AI Configuration</h1>
            <form method="post">
                <table class="form-table">
                    <tr>
                        <th scope="row">AI API Key</th>
                        <td>
                            <input type="password" name="ai_api_key" value="<?php echo esc_attr($api_key); ?>" class="regular-text" />
                            <p class="description">Enter your OpenAI API key for AI functionality</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">AI Model</th>
                        <td>
                            <select name="ai_model">
                                <option value="gpt-3.5-turbo" <?php selected($model, 'gpt-3.5-turbo'); ?>>GPT-3.5 Turbo</option>
                                <option value="gpt-4" <?php selected($model, 'gpt-4'); ?>>GPT-4</option>
                                <option value="gpt-4-turbo" <?php selected($model, 'gpt-4-turbo'); ?>>GPT-4 Turbo</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Temperature</th>
                        <td>
                            <input type="number" name="ai_temperature" value="<?php echo esc_attr($temperature); ?>" min="0" max="1" step="0.1" />
                            <p class="description">Controls randomness (0 = focused, 1 = creative)</p>
                        </td>
                    </tr>
                </table>
                <?php submit_button('Save Settings', 'primary', 'save_settings'); ?>
            </form>
            
            <h2>Test AI Connection</h2>
            <button id="test-ai" class="button">Test AI Response</button>
            <div id="test-result" style="margin-top: 15px;"></div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            $('#test-ai').click(function() {
                $('#test-result').html('Testing...');
                $.post(ajaxurl, {
                    action: 'starpath_test_ai',
                    nonce: '<?php echo wp_create_nonce('starpath_ai_test'); ?>'
                }, function(response) {
                    if (response.success) {
                        $('#test-result').html('<div class="notice notice-success"><p>✅ AI connection successful!</p></div>');
                    } else {
                        $('#test-result').html('<div class="notice notice-error"><p>❌ AI connection failed: ' + response.data.message + '</p></div>');
                    }
                });
            });
        });
        </script>
        <?php
    }
    
    /**
     * Handle AI chat requests
     */
    public function handle_ai_chat() {
        check_ajax_referer('starpath_nonce', 'nonce');
        
        $user_id = get_current_user_id();
        $message = sanitize_text_field($_POST['message']);
        
        // Check usage limits
        if (!starpath_can_use_ai($user_id)) {
            wp_send_json_error(array(
                'message' => 'Daily AI limit reached. <a href="/pricing/">Upgrade to Premium</a> for unlimited access.'
            ));
        }
        
        // Get AI response
        $ai_response = $this->get_ai_response($message, 'ai_reading');
        
        if ($ai_response) {
            // Increment usage counter
            starpath_increment_ai_usage($user_id);
            
            // Save conversation
            $this->save_ai_conversation($user_id, $message, $ai_response);
            
            wp_send_json_success(array(
                'message' => $ai_response,
                'remaining_uses' => starpath_is_premium_user($user_id) ? -1 : (STARPATH_FREE_AI_LIMIT - starpath_get_daily_ai_usage($user_id))
            ));
        } else {
            wp_send_json_error(array(
                'message' => 'Sorry, I\'m having trouble connecting to the cosmic network. Please try again in a moment.'
            ));
        }
    }
    
    /**
     * Handle birth chart generation
     */
    public function handle_birth_chart() {
        check_ajax_referer('starpath_nonce', 'nonce');
        
        $birth_date = sanitize_text_field($_POST['birth_date']);
        $birth_time = sanitize_text_field($_POST['birth_time']);
        $birth_location = sanitize_text_field($_POST['birth_location']);
        
        // Create birth chart prompt
        $prompt = "Generate a detailed birth chart interpretation for someone born on {$birth_date} at {$birth_time} in {$birth_location}.";
        
        $ai_response = $this->get_ai_response($prompt, 'birth_chart');
        
        if ($ai_response) {
            $user_id = get_current_user_id();
            
            // Save birth chart
            $chart_id = $this->save_birth_chart($user_id, $birth_date, $birth_time, $birth_location, $ai_response);
            
            $response_data = array('chart' => $ai_response);
            
            // Generate PDF for premium users
            if (starpath_is_premium_user($user_id)) {
                $pdf_url = $this->generate_birth_chart_pdf($chart_id, $ai_response);
                if ($pdf_url) {
                    $response_data['pdf_url'] = $pdf_url;
                }
            }
            
            wp_send_json_success($response_data);
        } else {
            wp_send_json_error(array(
                'message' => 'Unable to generate birth chart at this time. Please try again.'
            ));
        }
    }
    
    /**
     * Handle compatibility readings
     */
    public function handle_compatibility() {
        check_ajax_referer('starpath_nonce', 'nonce');
        
        $person1_sign = sanitize_text_field($_POST['person1_sign']);
        $person2_sign = sanitize_text_field($_POST['person2_sign']);
        
        $prompt = "Analyze the astrological compatibility between {$person1_sign} and {$person2_sign}. Provide insights into their relationship dynamics, strengths, challenges, and advice for harmony.";
        
        $ai_response = $this->get_ai_response($prompt, 'compatibility');
        
        if ($ai_response) {
            wp_send_json_success(array('reading' => $ai_response));
        } else {
            wp_send_json_error(array(
                'message' => 'Unable to generate compatibility reading. Please try again.'
            ));
        }
    }
    
    /**
     * Handle dream interpretation
     */
    public function handle_dream_interpretation() {
        check_ajax_referer('starpath_nonce', 'nonce');
        
        $user_id = get_current_user_id();
        $dream_description = sanitize_textarea_field($_POST['dream_description']);
        $dream_emotions = sanitize_text_field($_POST['dream_emotions']);
        
        // Check usage limits for free users
        if (!starpath_is_premium_user($user_id)) {
            $daily_dreams = get_user_meta($user_id, 'daily_dream_count_' . date('Y-m-d'), true);
            if ($daily_dreams >= 1) {
                wp_send_json_error(array(
                    'message' => 'Free users can interpret 1 dream per day. <a href="/pricing/">Upgrade to Premium</a> for unlimited interpretations.'
                ));
            }
        }
        
        $prompt = "Interpret this dream: {$dream_description}. The dreamer felt {$dream_emotions}. Provide symbolic meanings, psychological insights, and spiritual guidance.";
        
        $ai_response = $this->get_ai_response($prompt, 'dream_interpreter');
        
        if ($ai_response) {
            // Save dream interpretation
            $dream_id = $this->save_dream_interpretation($user_id, $dream_description, $dream_emotions, $ai_response);
            
            // Update daily count for free users
            if (!starpath_is_premium_user($user_id)) {
                update_user_meta($user_id, 'daily_dream_count_' . date('Y-m-d'), 1);
            }
            
            $response_data = array('interpretation' => $ai_response);
            
            // Generate PDF for premium users
            if (starpath_is_premium_user($user_id)) {
                $pdf_url = $this->generate_dream_pdf($dream_id, $ai_response);
                if ($pdf_url) {
                    $response_data['pdf_url'] = $pdf_url;
                }
            }
            
            wp_send_json_success($response_data);
        } else {
            wp_send_json_error(array(
                'message' => 'Unable to interpret dream at this time. Please try again.'
            ));
        }
    }

    /**
     * Get AI response from OpenAI API
     */
    private function get_ai_response($message, $prompt_type = 'ai_reading') {
        $api_key = get_option('starpath_ai_api_key');
        if (empty($api_key)) {
            return false;
        }

        $model = get_option('starpath_ai_model', 'gpt-3.5-turbo');
        $temperature = get_option('starpath_ai_temperature', 0.7);
        $system_prompt = isset($this->ai_prompts[$prompt_type]) ? $this->ai_prompts[$prompt_type] : $this->ai_prompts['ai_reading'];

        $data = array(
            'model' => $model,
            'messages' => array(
                array(
                    'role' => 'system',
                    'content' => $system_prompt
                ),
                array(
                    'role' => 'user',
                    'content' => $message
                )
            ),
            'temperature' => $temperature,
            'max_tokens' => 1000
        );

        $response = wp_remote_post('https://api.openai.com/v1/chat/completions', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode($data),
            'timeout' => 30
        ));

        if (is_wp_error($response)) {
            return false;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (isset($data['choices'][0]['message']['content'])) {
            return trim($data['choices'][0]['message']['content']);
        }

        return false;
    }

    /**
     * Save AI conversation
     */
    private function save_ai_conversation($user_id, $user_message, $ai_response) {
        $conversation_data = array(
            'user_id' => $user_id,
            'user_message' => $user_message,
            'ai_response' => $ai_response,
            'timestamp' => current_time('mysql')
        );

        // Save to custom table or post meta
        $conversations = get_user_meta($user_id, 'starpath_ai_conversations', true);
        if (!is_array($conversations)) {
            $conversations = array();
        }

        $conversations[] = $conversation_data;

        // Keep only last 50 conversations
        if (count($conversations) > 50) {
            $conversations = array_slice($conversations, -50);
        }

        update_user_meta($user_id, 'starpath_ai_conversations', $conversations);
    }

    /**
     * Save birth chart
     */
    private function save_birth_chart($user_id, $birth_date, $birth_time, $birth_location, $interpretation) {
        $chart_data = array(
            'post_title' => 'Birth Chart - ' . date('Y-m-d H:i:s'),
            'post_content' => $interpretation,
            'post_status' => 'private',
            'post_type' => 'birth_chart',
            'post_author' => $user_id
        );

        $chart_id = wp_insert_post($chart_data);

        if ($chart_id) {
            update_post_meta($chart_id, 'birth_date', $birth_date);
            update_post_meta($chart_id, 'birth_time', $birth_time);
            update_post_meta($chart_id, 'birth_location', $birth_location);
            update_post_meta($chart_id, 'user_id', $user_id);
        }

        return $chart_id;
    }

    /**
     * Save dream interpretation
     */
    private function save_dream_interpretation($user_id, $dream_description, $emotions, $interpretation) {
        $dream_data = array(
            'post_title' => 'Dream - ' . date('Y-m-d H:i:s'),
            'post_content' => $interpretation,
            'post_status' => 'private',
            'post_type' => 'dream_interpretation',
            'post_author' => $user_id
        );

        $dream_id = wp_insert_post($dream_data);

        if ($dream_id) {
            update_post_meta($dream_id, 'dream_description', $dream_description);
            update_post_meta($dream_id, 'dream_emotions', $emotions);
            update_post_meta($dream_id, 'user_id', $user_id);
        }

        return $dream_id;
    }

    /**
     * Generate PDF for birth chart (premium feature)
     */
    private function generate_birth_chart_pdf($chart_id, $content) {
        if (!class_exists('TCPDF')) {
            return false;
        }

        // Create PDF
        $pdf = new TCPDF();
        $pdf->AddPage();
        $pdf->SetFont('helvetica', '', 12);

        // Add content
        $html = '<h1 style="color: #FFD700;">Your Birth Chart Analysis</h1>';
        $html .= '<div style="color: #333;">' . nl2br($content) . '</div>';

        $pdf->writeHTML($html, true, false, true, false, '');

        // Save PDF
        $upload_dir = wp_upload_dir();
        $pdf_path = $upload_dir['path'] . '/birth-chart-' . $chart_id . '.pdf';
        $pdf_url = $upload_dir['url'] . '/birth-chart-' . $chart_id . '.pdf';

        $pdf->Output($pdf_path, 'F');

        return file_exists($pdf_path) ? $pdf_url : false;
    }

    /**
     * Generate PDF for dream interpretation (premium feature)
     */
    private function generate_dream_pdf($dream_id, $content) {
        if (!class_exists('TCPDF')) {
            return false;
        }

        // Create PDF
        $pdf = new TCPDF();
        $pdf->AddPage();
        $pdf->SetFont('helvetica', '', 12);

        // Add content
        $html = '<h1 style="color: #FFD700;">Your Dream Interpretation</h1>';
        $html .= '<div style="color: #333;">' . nl2br($content) . '</div>';

        $pdf->writeHTML($html, true, false, true, false, '');

        // Save PDF
        $upload_dir = wp_upload_dir();
        $pdf_path = $upload_dir['path'] . '/dream-interpretation-' . $dream_id . '.pdf';
        $pdf_url = $upload_dir['url'] . '/dream-interpretation-' . $dream_id . '.pdf';

        $pdf->Output($pdf_path, 'F');

        return file_exists($pdf_path) ? $pdf_url : false;
    }

    /**
     * Shortcode for AI chat
     */
    public function ai_chat_shortcode($atts) {
        $atts = shortcode_atts(array(
            'height' => '400px'
        ), $atts);

        ob_start();
        ?>
        <div class="starpath-ai-chat" style="height: <?php echo esc_attr($atts['height']); ?>;">
            <div id="ai-chat-messages" class="ai-chat-messages"></div>
            <form id="ai-chat-form" class="ai-chat-form">
                <div class="chat-input-group">
                    <input type="text" id="ai-message" placeholder="Ask me anything about astrology..." required>
                    <button type="submit" class="btn btn-primary">Send ✨</button>
                </div>
            </form>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Shortcode for birth chart
     */
    public function birth_chart_shortcode($atts) {
        ob_start();
        ?>
        <div class="starpath-birth-chart">
            <form id="birth-chart-form" class="starpath-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="birth_date">Birth Date</label>
                        <input type="date" id="birth_date" name="birth_date" required>
                    </div>
                    <div class="form-group">
                        <label for="birth_time">Birth Time</label>
                        <input type="time" id="birth_time" name="birth_time" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="birth_location">Birth Location</label>
                    <input type="text" id="birth_location" name="birth_location" placeholder="City, Country" required>
                </div>
                <button type="submit" class="btn btn-primary">Generate Birth Chart 🌟</button>
            </form>
            <div id="birth-chart-result" class="chart-result"></div>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Shortcode for compatibility
     */
    public function compatibility_shortcode($atts) {
        $zodiac_signs = array(
            'aries' => 'Aries ♈', 'taurus' => 'Taurus ♉', 'gemini' => 'Gemini ♊',
            'cancer' => 'Cancer ♋', 'leo' => 'Leo ♌', 'virgo' => 'Virgo ♍',
            'libra' => 'Libra ♎', 'scorpio' => 'Scorpio ♏', 'sagittarius' => 'Sagittarius ♐',
            'capricorn' => 'Capricorn ♑', 'aquarius' => 'Aquarius ♒', 'pisces' => 'Pisces ♓'
        );

        ob_start();
        ?>
        <div class="starpath-compatibility">
            <form id="compatibility-form" class="starpath-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="person1_sign">Your Sign</label>
                        <select id="person1_sign" name="person1_sign" required>
                            <option value="">Select Your Sign</option>
                            <?php foreach ($zodiac_signs as $value => $label): ?>
                                <option value="<?php echo esc_attr($value); ?>"><?php echo esc_html($label); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="person2_sign">Partner's Sign</label>
                        <select id="person2_sign" name="person2_sign" required>
                            <option value="">Select Partner's Sign</option>
                            <?php foreach ($zodiac_signs as $value => $label): ?>
                                <option value="<?php echo esc_attr($value); ?>"><?php echo esc_html($label); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">Get Compatibility Reading 💕</button>
            </form>
            <div id="compatibility-result" class="reading-result"></div>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Shortcode for dream interpreter
     */
    public function dream_interpreter_shortcode($atts) {
        $emotions = array(
            'happy' => 'Happy 😊', 'scared' => 'Scared 😨', 'confused' => 'Confused 😕',
            'peaceful' => 'Peaceful 😌', 'anxious' => 'Anxious 😰', 'excited' => 'Excited 🤩',
            'sad' => 'Sad 😢', 'curious' => 'Curious 🤔', 'angry' => 'Angry 😠'
        );

        ob_start();
        ?>
        <div class="starpath-dream-interpreter">
            <form id="dream-form" class="starpath-form">
                <div class="form-group">
                    <label for="dream_description">Describe Your Dream</label>
                    <textarea id="dream_description" name="dream_description" rows="6" placeholder="Tell me about your dream in detail..." required></textarea>
                </div>
                <div class="form-group">
                    <label for="dream_emotions">How did the dream make you feel?</label>
                    <select id="dream_emotions" name="dream_emotions">
                        <option value="">Select emotion</option>
                        <?php foreach ($emotions as $value => $label): ?>
                            <option value="<?php echo esc_attr($value); ?>"><?php echo esc_html($label); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary">Interpret Dream 🌙</button>
            </form>
            <div id="dream-result" class="interpretation-result"></div>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Shortcode for horoscope
     */
    public function horoscope_shortcode($atts) {
        $atts = shortcode_atts(array(
            'type' => 'daily',
            'sign' => ''
        ), $atts);

        // This would typically fetch from a horoscope API or database
        // For now, we'll return a placeholder
        return '<div class="starpath-horoscope">Horoscope content for ' . esc_html($atts['sign']) . ' (' . esc_html($atts['type']) . ')</div>';
    }
}

// Initialize the plugin
new StarPath_AI();
