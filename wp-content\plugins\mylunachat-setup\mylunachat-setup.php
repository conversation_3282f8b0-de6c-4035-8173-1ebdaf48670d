<?php
/**
 * Plugin Name: MyLunaChat Setup & Configuration
 * Plugin URI: https://mylunachat.com
 * Description: Automated setup and configuration plugin for the MyLunaChat.com Astrology Platform
 * Version: 1.0.0
 * Author: MyLunaChat Development Team
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('MYLUNACHAT_SETUP_VERSION', '1.0.0');
define('MYLUNACHAT_SETUP_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('MYLUNACHAT_SETUP_PLUGIN_URL', plugin_dir_url(__FILE__));

/**
 * Main MyLunaChat Setup Class
 */
class MyLunaChat_Setup {
    
    private $required_plugins = array(
        'elementor' => array(
            'name' => 'Elementor',
            'slug' => 'elementor/elementor.php',
            'source' => 'repo'
        ),
        'memberpress' => array(
            'name' => 'MemberPress',
            'slug' => 'memberpress/memberpress.php',
            'source' => 'premium'
        ),
        'wpforms-lite' => array(
            'name' => 'WPForms Lite',
            'slug' => 'wpforms-lite/wpforms.php',
            'source' => 'repo'
        ),
        'ad-inserter' => array(
            'name' => 'Ad Inserter',
            'slug' => 'ad-inserter/ad-inserter.php',
            'source' => 'repo'
        ),
        'wp-rocket' => array(
            'name' => 'WP Rocket',
            'slug' => 'wp-rocket/wp-rocket.php',
            'source' => 'premium'
        ),
        'wordpress-seo' => array(
            'name' => 'Yoast SEO',
            'slug' => 'wordpress-seo/wp-seo.php',
            'source' => 'repo'
        ),
        'google-site-kit' => array(
            'name' => 'Google Site Kit',
            'slug' => 'google-site-kit/google-site-kit.php',
            'source' => 'repo'
        ),
        'mailpoet' => array(
            'name' => 'MailPoet',
            'slug' => 'mailpoet/mailpoet.php',
            'source' => 'repo'
        ),
        'astra' => array(
            'name' => 'Astra',
            'slug' => 'astra/astra.php',
            'source' => 'repo',
            'type' => 'theme'
        )
    );
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('wp_ajax_starpath_install_plugin', array($this, 'ajax_install_plugin'));
        add_action('wp_ajax_starpath_create_pages', array($this, 'ajax_create_pages'));
        add_action('wp_ajax_starpath_configure_settings', array($this, 'ajax_configure_settings'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            'MyLunaChat Setup',
            'MyLunaChat Setup',
            'manage_options',
            'mylunachat-setup',
            array($this, 'admin_page'),
            'dashicons-star-filled',
            2
        );
    }
    
    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if ($hook !== 'toplevel_page_mylunachat-setup') {
            return;
        }

        wp_enqueue_script('mylunachat-setup-js', MYLUNACHAT_SETUP_PLUGIN_URL . 'js/setup.js', array('jquery'), MYLUNACHAT_SETUP_VERSION, true);
        wp_enqueue_style('mylunachat-setup-css', MYLUNACHAT_SETUP_PLUGIN_URL . 'css/setup.css', array(), MYLUNACHAT_SETUP_VERSION);

        wp_localize_script('mylunachat-setup-js', 'mylunachat_setup', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('mylunachat_setup_nonce')
        ));
    }
    
    /**
     * Admin page HTML
     */
    public function admin_page() {
        ?>
        <div class="wrap mylunachat-setup-wrap">
            <h1>🌙 MyLunaChat.com Astrology Platform Setup</h1>
            <p>Welcome to MyLunaChat! This setup wizard will help you configure your astrology platform.</p>

            <div class="mylunachat-setup-progress">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%"></div>
                </div>
                <div class="progress-text">Ready to begin setup</div>
            </div>
            
            <div class="mylunachat-setup-steps">
                
                <!-- Step 1: Plugin Installation -->
                <div class="setup-step" id="step-plugins">
                    <h2>Step 1: Install Required Plugins</h2>
                    <p>Installing essential plugins for your astrology platform...</p>
                    
                    <div class="plugin-list">
                        <?php foreach ($this->required_plugins as $plugin_key => $plugin_data): ?>
                            <div class="plugin-item" data-plugin="<?php echo esc_attr($plugin_key); ?>">
                                <span class="plugin-name"><?php echo esc_html($plugin_data['name']); ?></span>
                                <span class="plugin-status">Pending</span>
                                <div class="plugin-progress"></div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <button class="button button-primary" id="install-plugins">Install All Plugins</button>
                </div>
                
                <!-- Step 2: Create Pages -->
                <div class="setup-step" id="step-pages" style="display: none;">
                    <h2>Step 2: Create Site Pages</h2>
                    <p>Creating all necessary pages with proper URL structure...</p>
                    
                    <div class="pages-list">
                        <div class="page-item">Home Page</div>
                        <div class="page-item">Daily Horoscope</div>
                        <div class="page-item">Weekly Horoscope</div>
                        <div class="page-item">Monthly Horoscope</div>
                        <div class="page-item">Birth Chart Generator</div>
                        <div class="page-item">Compatibility Reading</div>
                        <div class="page-item">1-on-1 AI Reading</div>
                        <div class="page-item">Dream Interpreter</div>
                        <div class="page-item">Moon Calendar</div>
                        <div class="page-item">Astrology 101</div>
                        <div class="page-item">Blog</div>
                        <div class="page-item">About</div>
                        <div class="page-item">Contact</div>
                        <div class="page-item">Dashboard</div>
                        <div class="page-item">Login/Register</div>
                    </div>
                    
                    <button class="button button-primary" id="create-pages">Create All Pages</button>
                </div>
                
                <!-- Step 3: Configure Settings -->
                <div class="setup-step" id="step-settings" style="display: none;">
                    <h2>Step 3: Configure Settings</h2>
                    <p>Setting up theme, membership plans, and basic configurations...</p>
                    
                    <div class="settings-list">
                        <div class="setting-item">Activate Astra Theme</div>
                        <div class="setting-item">Configure MemberPress</div>
                        <div class="setting-item">Set up SEO Settings</div>
                        <div class="setting-item">Configure Google Fonts</div>
                        <div class="setting-item">Set up Permalinks</div>
                    </div>
                    
                    <button class="button button-primary" id="configure-settings">Configure Settings</button>
                </div>
                
                <!-- Step 4: Complete -->
                <div class="setup-step" id="step-complete" style="display: none;">
                    <h2>🎉 Setup Complete!</h2>
                    <p>Your MyLunaChat.com astrology platform is now ready!</p>
                    
                    <div class="completion-actions">
                        <a href="<?php echo home_url(); ?>" class="button button-primary">View Your Site</a>
                        <a href="<?php echo admin_url('customize.php'); ?>" class="button">Customize Theme</a>
                        <a href="<?php echo admin_url('edit.php?post_type=page'); ?>" class="button">Manage Pages</a>
                    </div>
                    
                    <div class="next-steps">
                        <h3>Next Steps:</h3>
                        <ul>
                            <li>Configure your database settings in wp-config.php</li>
                            <li>Set up your AI API keys for chat functionality</li>
                            <li>Configure Google AdSense for monetization</li>
                            <li>Set up SSL certificate for security</li>
                            <li>Test all features and customize as needed</li>
                        </ul>
                    </div>
                </div>
                
            </div>
            
            <div class="mylunachat-setup-footer">
                <p>Need help? Check out our <a href="#" target="_blank">documentation</a> or <a href="#" target="_blank">contact support</a>.</p>
            </div>
        </div>
        <?php
    }
    
    /**
     * AJAX handler for plugin installation
     */
    public function ajax_install_plugin() {
        check_ajax_referer('mylunachat_setup_nonce', 'nonce');
        
        if (!current_user_can('install_plugins')) {
            wp_die('Insufficient permissions');
        }
        
        $plugin_key = sanitize_text_field($_POST['plugin']);
        
        if (!isset($this->required_plugins[$plugin_key])) {
            wp_send_json_error('Invalid plugin');
        }
        
        $plugin_data = $this->required_plugins[$plugin_key];
        
        // For demo purposes, we'll simulate installation
        // In a real implementation, you'd use WordPress plugin installation APIs
        
        wp_send_json_success(array(
            'message' => $plugin_data['name'] . ' installed successfully',
            'plugin' => $plugin_key
        ));
    }
    
    /**
     * AJAX handler for page creation
     */
    public function ajax_create_pages() {
        check_ajax_referer('mylunachat_setup_nonce', 'nonce');
        
        if (!current_user_can('edit_pages')) {
            wp_die('Insufficient permissions');
        }
        
        $pages = $this->get_required_pages();
        $created_pages = array();
        
        foreach ($pages as $page_data) {
            $page_id = wp_insert_post(array(
                'post_title' => $page_data['title'],
                'post_content' => $page_data['content'],
                'post_status' => 'publish',
                'post_type' => 'page',
                'post_name' => $page_data['slug']
            ));
            
            if ($page_id && !is_wp_error($page_id)) {
                $created_pages[] = $page_data['title'];
                
                // Set custom template if specified
                if (isset($page_data['template'])) {
                    update_post_meta($page_id, '_wp_page_template', $page_data['template']);
                }
            }
        }
        
        wp_send_json_success(array(
            'message' => 'Pages created successfully',
            'pages' => $created_pages
        ));
    }
    
    /**
     * AJAX handler for settings configuration
     */
    public function ajax_configure_settings() {
        check_ajax_referer('mylunachat_setup_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }
        
        // Configure basic WordPress settings
        update_option('blogname', 'MyLunaChat.com - Astrology & AI Insights');
        update_option('blogdescription', 'Your personal astrology platform with AI-powered insights');
        update_option('permalink_structure', '/%postname%/');
        
        // Set front page
        $home_page = get_page_by_path('home');
        if ($home_page) {
            update_option('show_on_front', 'page');
            update_option('page_on_front', $home_page->ID);
        }
        
        // Set blog page
        $blog_page = get_page_by_path('blog');
        if ($blog_page) {
            update_option('page_for_posts', $blog_page->ID);
        }
        
        wp_send_json_success(array(
            'message' => 'Settings configured successfully'
        ));
    }
    
    /**
     * Get required pages data
     */
    private function get_required_pages() {
        return array(
            array(
                'title' => 'Home',
                'slug' => 'home',
                'content' => $this->get_home_page_content()
            ),
            array(
                'title' => 'Daily Horoscope',
                'slug' => 'horoscope/daily',
                'content' => $this->get_horoscope_page_content('daily')
            ),
            array(
                'title' => 'Weekly Horoscope',
                'slug' => 'horoscope/weekly',
                'content' => $this->get_horoscope_page_content('weekly')
            ),
            array(
                'title' => 'Monthly Horoscope',
                'slug' => 'horoscope/monthly',
                'content' => $this->get_horoscope_page_content('monthly')
            ),
            array(
                'title' => 'Birth Chart Generator',
                'slug' => 'birth-chart',
                'content' => $this->get_birth_chart_page_content()
            ),
            array(
                'title' => 'Compatibility Reading',
                'slug' => 'compatibility',
                'content' => $this->get_compatibility_page_content()
            ),
            array(
                'title' => '1-on-1 AI Reading',
                'slug' => 'ai-reading',
                'content' => $this->get_ai_reading_page_content()
            ),
            array(
                'title' => 'Dream Interpreter',
                'slug' => 'dream-interpreter',
                'content' => $this->get_dream_interpreter_page_content()
            ),
            array(
                'title' => 'Moon Calendar',
                'slug' => 'moon-calendar',
                'content' => $this->get_moon_calendar_page_content()
            ),
            array(
                'title' => 'Astrology 101',
                'slug' => 'learn',
                'content' => $this->get_learn_page_content()
            ),
            array(
                'title' => 'Blog',
                'slug' => 'blog',
                'content' => 'Welcome to our astrology blog! Here you\'ll find the latest insights, tips, and cosmic wisdom.'
            ),
            array(
                'title' => 'About',
                'slug' => 'about',
                'content' => $this->get_about_page_content()
            ),
            array(
                'title' => 'Contact',
                'slug' => 'contact',
                'content' => $this->get_contact_page_content()
            ),
            array(
                'title' => 'Dashboard',
                'slug' => 'dashboard',
                'content' => $this->get_dashboard_page_content()
            ),
            array(
                'title' => 'Login',
                'slug' => 'login',
                'content' => $this->get_login_page_content()
            )
        );
    }
    
    /**
     * Get home page content
     */
    private function get_home_page_content() {
        return '
        <div class="hero-section celestial-bg">
            <h1>Welcome to MyLunaChat.com 🌙✨</h1>
            <p class="hero-subtitle">Your Personal Astrology & AI Insight Platform</p>
            <div class="hero-buttons">
                <a href="/birth-chart/" class="btn btn-primary">Generate Birth Chart</a>
                <a href="/ai-reading/" class="btn btn-secondary">AI Reading</a>
            </div>
        </div>

        <div class="features-section">
            <h2>Discover Your Cosmic Journey</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <span class="zodiac-symbol">♈</span>
                    <h3>Daily Horoscopes</h3>
                    <p>Get personalized daily insights based on your zodiac sign</p>
                </div>
                <div class="feature-card">
                    <span class="zodiac-symbol">🌙</span>
                    <h3>Birth Chart Analysis</h3>
                    <p>Detailed natal chart interpretation with AI insights</p>
                </div>
                <div class="feature-card">
                    <span class="zodiac-symbol">💫</span>
                    <h3>AI Astrologer</h3>
                    <p>Chat with our AI astrologer for personalized guidance</p>
                </div>
            </div>
        </div>';
    }

    /**
     * Get horoscope page content
     */
    private function get_horoscope_page_content($type) {
        $title = ucfirst($type) . ' Horoscope';
        return "
        <div class='horoscope-header'>
            <h1>{$title}</h1>
            <p>Discover what the stars have in store for you</p>
        </div>

        <div class='zodiac-grid'>
            <!-- Zodiac signs will be populated by JavaScript/AJAX -->
        </div>

        <div class='premium-upgrade'>
            <h3>Want More Detailed Readings?</h3>
            <p>Upgrade to Premium for personalized insights and ad-free experience</p>
            <a href='/pricing/' class='btn btn-premium'>Upgrade Now</a>
        </div>";
    }

    /**
     * Get birth chart page content
     */
    private function get_birth_chart_page_content() {
        return '
        <div class="birth-chart-header">
            <h1>Birth Chart Generator 🌟</h1>
            <p>Discover your cosmic blueprint with our AI-powered birth chart analysis</p>
        </div>

        <form id="birth-chart-form" class="starpath-form">
            <div class="form-group">
                <label for="birth_date">Birth Date</label>
                <input type="date" id="birth_date" name="birth_date" required>
            </div>
            <div class="form-group">
                <label for="birth_time">Birth Time</label>
                <input type="time" id="birth_time" name="birth_time" required>
            </div>
            <div class="form-group">
                <label for="birth_location">Birth Location</label>
                <input type="text" id="birth_location" name="birth_location" placeholder="City, Country" required>
            </div>
            <button type="submit" class="btn btn-primary">Generate Chart</button>
        </form>

        <div id="birth-chart-result" class="chart-result"></div>';
    }

    /**
     * Get compatibility page content
     */
    private function get_compatibility_page_content() {
        return '
        <div class="compatibility-header">
            <h1>Compatibility Reading 💕</h1>
            <p>Discover your cosmic connection with someone special</p>
        </div>

        <form id="compatibility-form" class="starpath-form">
            <div class="person-section">
                <h3>Person 1</h3>
                <div class="form-group">
                    <label for="person1_sign">Zodiac Sign</label>
                    <select id="person1_sign" name="person1_sign" required>
                        <option value="">Select Sign</option>
                        <option value="aries">Aries</option>
                        <option value="taurus">Taurus</option>
                        <option value="gemini">Gemini</option>
                        <option value="cancer">Cancer</option>
                        <option value="leo">Leo</option>
                        <option value="virgo">Virgo</option>
                        <option value="libra">Libra</option>
                        <option value="scorpio">Scorpio</option>
                        <option value="sagittarius">Sagittarius</option>
                        <option value="capricorn">Capricorn</option>
                        <option value="aquarius">Aquarius</option>
                        <option value="pisces">Pisces</option>
                    </select>
                </div>
            </div>

            <div class="person-section">
                <h3>Person 2</h3>
                <div class="form-group">
                    <label for="person2_sign">Zodiac Sign</label>
                    <select id="person2_sign" name="person2_sign" required>
                        <option value="">Select Sign</option>
                        <option value="aries">Aries</option>
                        <option value="taurus">Taurus</option>
                        <option value="gemini">Gemini</option>
                        <option value="cancer">Cancer</option>
                        <option value="leo">Leo</option>
                        <option value="virgo">Virgo</option>
                        <option value="libra">Libra</option>
                        <option value="scorpio">Scorpio</option>
                        <option value="sagittarius">Sagittarius</option>
                        <option value="capricorn">Capricorn</option>
                        <option value="aquarius">Aquarius</option>
                        <option value="pisces">Pisces</option>
                    </select>
                </div>
            </div>

            <button type="submit" class="btn btn-primary">Get Compatibility Reading</button>
        </form>

        <div id="compatibility-result" class="reading-result"></div>';
    }

    /**
     * Get AI reading page content
     */
    private function get_ai_reading_page_content() {
        return '
        <div class="ai-reading-header">
            <h1>1-on-1 AI Astrologer 🤖✨</h1>
            <p>Chat with our AI astrologer for personalized guidance and insights</p>
        </div>

        <div class="ai-chat-container">
            <div id="ai-chat-messages" class="chat-messages">
                <div class="ai-message">
                    Hello! I\'m your AI astrologer. I\'m here to provide you with cosmic guidance and insights. What would you like to know about your astrological journey?
                </div>
            </div>

            <form id="ai-chat-form" class="chat-form">
                <div class="chat-input-group">
                    <input type="text" id="ai-message" placeholder="Ask me anything about astrology..." required>
                    <button type="submit" class="btn btn-primary">Send</button>
                </div>
            </form>
        </div>

        <div class="usage-info">
            <p class="free-user-info">Free users: 3 questions per day | <a href="/pricing/">Upgrade for unlimited access</a></p>
        </div>';
    }

    /**
     * Get dream interpreter page content
     */
    private function get_dream_interpreter_page_content() {
        return '
        <div class="dream-header">
            <h1>Dream Interpreter 🌙💭</h1>
            <p>Unlock the hidden meanings in your dreams with AI-powered interpretation</p>
        </div>

        <form id="dream-form" class="starpath-form">
            <div class="form-group">
                <label for="dream_description">Describe Your Dream</label>
                <textarea id="dream_description" name="dream_description" rows="6" placeholder="Tell me about your dream in detail..." required></textarea>
            </div>
            <div class="form-group">
                <label for="dream_emotions">How did the dream make you feel?</label>
                <select id="dream_emotions" name="dream_emotions">
                    <option value="">Select emotion</option>
                    <option value="happy">Happy</option>
                    <option value="scared">Scared</option>
                    <option value="confused">Confused</option>
                    <option value="peaceful">Peaceful</option>
                    <option value="anxious">Anxious</option>
                    <option value="excited">Excited</option>
                    <option value="sad">Sad</option>
                    <option value="curious">Curious</option>
                </select>
            </div>
            <button type="submit" class="btn btn-primary">Interpret Dream</button>
        </form>

        <div id="dream-result" class="interpretation-result"></div>';
    }

    /**
     * Get remaining page content methods
     */
    private function get_moon_calendar_page_content() {
        return '
        <div class="moon-calendar-header">
            <h1>Moon Calendar 🌙</h1>
            <p>Track lunar phases and their influence on your life</p>
        </div>

        <div id="moon-calendar" class="calendar-container">
            <!-- Calendar will be populated by JavaScript -->
        </div>';
    }

    private function get_learn_page_content() {
        return '
        <div class="learn-header">
            <h1>Astrology 101 📚</h1>
            <p>Learn the fundamentals of astrology and cosmic wisdom</p>
        </div>

        <div class="learning-modules">
            <div class="module-card">
                <h3>Zodiac Signs</h3>
                <p>Understand the 12 zodiac signs and their characteristics</p>
            </div>
            <div class="module-card">
                <h3>Planets & Houses</h3>
                <p>Learn about planetary influences and astrological houses</p>
            </div>
            <div class="module-card">
                <h3>Birth Chart Basics</h3>
                <p>How to read and interpret your natal chart</p>
            </div>
        </div>';
    }

    private function get_about_page_content() {
        return '
        <div class="about-header">
            <h1>About MyLunaChat.com 🌙✨</h1>
            <p>Your trusted companion for astrological insights and cosmic guidance</p>
        </div>

        <div class="about-content">
            <p>MyLunaChat.com combines ancient astrological wisdom with modern AI technology to provide you with personalized cosmic insights. Our platform offers everything from daily horoscopes to detailed birth chart analysis, all powered by advanced artificial intelligence.</p>

            <h2>Our Mission</h2>
            <p>To make astrology accessible, accurate, and personally meaningful for everyone on their spiritual journey.</p>
        </div>';
    }

    private function get_contact_page_content() {
        return '
        <div class="contact-header">
            <h1>Contact Us 📧</h1>
            <p>Get in touch with our team</p>
        </div>

        <div class="contact-form">
            [wpforms id="1"]
        </div>';
    }

    private function get_dashboard_page_content() {
        return '
        <div class="dashboard-header">
            <h1>Your Dashboard 🌟</h1>
            <p>Welcome back! Here\'s your personalized astrology hub</p>
        </div>

        <div class="dashboard-content premium-required">
            <div class="dashboard-widgets">
                <div class="widget">
                    <h3>Your Birth Chart</h3>
                    <p>Quick access to your natal chart</p>
                </div>
                <div class="widget">
                    <h3>Recent Readings</h3>
                    <p>Your latest AI consultations</p>
                </div>
                <div class="widget">
                    <h3>Saved Dreams</h3>
                    <p>Your dream interpretation history</p>
                </div>
            </div>
        </div>';
    }

    private function get_login_page_content() {
        return '
        <div class="login-header">
            <h1>Sign In to StarPath 🔐</h1>
            <p>Access your personalized astrology experience</p>
        </div>

        <div class="login-forms">
            [memberpress_login]
            [memberpress_registration]
        </div>';
    }
}

// Initialize the plugin
new MyLunaChat_Setup();
