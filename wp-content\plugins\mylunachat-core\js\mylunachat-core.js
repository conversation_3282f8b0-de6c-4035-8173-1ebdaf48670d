/**
 * MyLunaChat Core JavaScript
 * Handles rewarded ads, feature loading, and UI interactions
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initMyLunaChatCore();
    });

    /**
     * Initialize core functionality
     */
    function initMyLunaChatCore() {
        initRewardedAds();
        initUsageWidget();
        initFormHandlers();
    }

    /**
     * Initialize rewarded ad functionality
     */
    function initRewardedAds() {
        // Handle continue button clicks
        window.mylunachatShowFeature = function(instanceId, feature) {
            showFeatureContent(instanceId, feature);
        };
    }

    /**
     * Show feature content after ad viewing
     */
    function showFeatureContent(instanceId, feature) {
        const adContainer = document.getElementById(instanceId + '_ad');
        const contentContainer = document.getElementById(instanceId + '_content');
        
        if (!adContainer || !contentContainer) {
            console.error('MyLunaChat: Container elements not found');
            return;
        }

        // Hide ad container with animation
        $(adContainer).fadeOut(500, function() {
            // Show content container
            $(contentContainer).fadeIn(500);
            
            // Load feature content via AJAX
            loadFeatureContent(instanceId, feature);
        });
    }

    /**
     * Load feature content via AJAX
     */
    function loadFeatureContent(instanceId, feature) {
        const contentContainer = $('#' + instanceId + '_content');
        
        $.ajax({
            url: mylunachat_core_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'mylunachat_load_feature',
                feature: feature,
                nonce: mylunachat_core_ajax.nonce
            },
            beforeSend: function() {
                contentContainer.html(`
                    <div class="loading-message">
                        <div class="cosmic-loader">🌙 ✨ 🌟</div>
                        <p>Consulting the cosmos...</p>
                    </div>
                `);
            },
            success: function(response) {
                if (response.success) {
                    contentContainer.html(response.data.content);
                    
                    // Update usage widget if present
                    updateUsageWidget(response.data.remaining_uses);
                    
                    // Trigger custom event
                    $(document).trigger('mylunachat:feature_loaded', {
                        feature: feature,
                        remaining_uses: response.data.remaining_uses
                    });
                } else {
                    showError(contentContainer, response.data || 'Failed to load content');
                }
            },
            error: function() {
                showError(contentContainer, 'Connection error. Please try again.');
            }
        });
    }

    /**
     * Show error message
     */
    function showError(container, message) {
        container.html(`
            <div class="error-message">
                <div class="error-icon">⚠️</div>
                <p>${message}</p>
                <button class="btn btn-secondary" onclick="location.reload()">Try Again</button>
            </div>
        `);
    }

    /**
     * Initialize usage widget functionality
     */
    function initUsageWidget() {
        // Auto-refresh usage widget every 5 minutes
        setInterval(function() {
            refreshUsageWidget();
        }, 300000); // 5 minutes

        // Listen for usage updates
        $(document).on('mylunachat:usage_updated', function(event, data) {
            updateUsageWidget(data.remaining_uses);
        });
    }

    /**
     * Refresh usage widget
     */
    function refreshUsageWidget() {
        const widgets = $('.mylunachat-usage-widget');
        
        widgets.each(function() {
            const widget = $(this);
            
            $.ajax({
                url: mylunachat_core_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'mylunachat_get_usage_stats',
                    nonce: mylunachat_core_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        updateUsageWidgetDisplay(widget, response.data);
                    }
                }
            });
        });
    }

    /**
     * Update usage widget display
     */
    function updateUsageWidget(remainingUses) {
        const usageCount = $('.usage-count');
        const usageProgress = $('.usage-progress');
        const usageRemaining = $('.usage-remaining');
        
        if (remainingUses !== 'unlimited') {
            const freeLimit = 3; // Should match PHP constant
            const used = freeLimit - remainingUses;
            const percentage = (used / freeLimit) * 100;
            
            usageCount.text(used + '/' + freeLimit);
            usageProgress.css('width', Math.min(100, percentage) + '%');
            
            if (remainingUses > 0) {
                usageRemaining.text(remainingUses + ' consultations remaining today');
            } else {
                usageRemaining.text('Daily limit reached. Resets at midnight.');
            }
        }
    }

    /**
     * Update usage widget with full data
     */
    function updateUsageWidgetDisplay(widget, data) {
        if (data.is_premium) {
            return; // Premium users don't need updates
        }
        
        const usageCount = widget.find('.usage-count');
        const usageProgress = widget.find('.usage-progress');
        const usageRemaining = widget.find('.usage-remaining');
        
        usageCount.text(data.daily_usage + '/' + data.free_limit);
        usageProgress.css('width', Math.min(100, data.percentage_used) + '%');
        
        if (data.remaining_uses > 0) {
            usageRemaining.text(data.remaining_uses + ' consultations remaining today');
        } else {
            usageRemaining.text('Daily limit reached. Resets at midnight.');
        }
    }

    /**
     * Initialize form handlers
     */
    function initFormHandlers() {
        // Handle AI form submissions
        $(document).on('submit', '.mylunachat-ai-form', function(e) {
            e.preventDefault();
            handleAIFormSubmission($(this));
        });

        // Handle premium upgrade prompts
        $(document).on('click', '.mylunachat-upgrade-prompt', function(e) {
            e.preventDefault();
            showUpgradeModal();
        });
    }

    /**
     * Handle AI form submission
     */
    function handleAIFormSubmission(form) {
        const submitBtn = form.find('button[type="submit"]');
        const originalText = submitBtn.text();
        
        // Check if user can use AI
        if (!canUseAI()) {
            showUpgradeModal();
            return;
        }
        
        // Disable submit button
        submitBtn.prop('disabled', true).text('Consulting the stars...');
        
        // Get form data
        const formData = form.serialize();
        const action = form.data('action') || 'mylunachat_ai_request';
        
        $.ajax({
            url: mylunachat_core_ajax.ajax_url,
            type: 'POST',
            data: formData + '&action=' + action + '&nonce=' + mylunachat_core_ajax.nonce,
            success: function(response) {
                if (response.success) {
                    displayAIResponse(form, response.data);
                    
                    // Update usage
                    updateUsageWidget(response.data.remaining_uses);
                    
                    // Trigger event
                    $(document).trigger('mylunachat:ai_response', response.data);
                } else {
                    showFormError(form, response.data.message || 'Request failed');
                }
            },
            error: function() {
                showFormError(form, 'Connection error. Please try again.');
            },
            complete: function() {
                // Re-enable submit button
                submitBtn.prop('disabled', false).text(originalText);
            }
        });
    }

    /**
     * Display AI response
     */
    function displayAIResponse(form, data) {
        const resultContainer = form.siblings('.ai-result').length ? 
            form.siblings('.ai-result') : 
            $('<div class="ai-result"></div>').insertAfter(form);
        
        resultContainer.html(`
            <div class="ai-response">
                <div class="response-header">
                    <h3>✨ Your Cosmic Insights</h3>
                </div>
                <div class="response-content">
                    ${data.message || data.response || data.content}
                </div>
                ${data.pdf_url ? `<div class="response-actions">
                    <a href="${data.pdf_url}" class="btn btn-premium" target="_blank">
                        Download PDF Report 📄
                    </a>
                </div>` : ''}
            </div>
        `).hide().fadeIn(500);
        
        // Scroll to result
        $('html, body').animate({
            scrollTop: resultContainer.offset().top - 100
        }, 500);
    }

    /**
     * Show form error
     */
    function showFormError(form, message) {
        const errorContainer = form.find('.form-error').length ?
            form.find('.form-error') :
            $('<div class="form-error"></div>').prependTo(form);
        
        errorContainer.html(`
            <div class="error-message">
                <span class="error-icon">⚠️</span>
                ${message}
            </div>
        `).hide().fadeIn(300);
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            errorContainer.fadeOut(300);
        }, 5000);
    }

    /**
     * Check if user can use AI
     */
    function canUseAI() {
        // This should be populated by PHP
        return window.mylunachat_can_use_ai !== false;
    }

    /**
     * Show upgrade modal
     */
    function showUpgradeModal() {
        const modal = $(`
            <div class="mylunachat-modal-overlay">
                <div class="mylunachat-modal">
                    <div class="modal-header">
                        <h2>🌟 Unlock Unlimited Cosmic Insights</h2>
                        <button class="modal-close">&times;</button>
                    </div>
                    <div class="modal-content">
                        <div class="upgrade-benefits">
                            <ul>
                                <li>✨ Unlimited AI consultations</li>
                                <li>📊 Detailed PDF reports</li>
                                <li>🚫 Ad-free experience</li>
                                <li>🌙 Personalized alerts</li>
                                <li>💫 Priority support</li>
                            </ul>
                        </div>
                        <div class="modal-actions">
                            <a href="/pricing/" class="btn btn-premium">
                                Upgrade Now - $10/month
                            </a>
                            <button class="btn btn-secondary modal-close">
                                Maybe Later
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `);
        
        $('body').append(modal);
        modal.fadeIn(300);
        
        // Close modal handlers
        modal.find('.modal-close, .mylunachat-modal-overlay').on('click', function(e) {
            if (e.target === this) {
                modal.fadeOut(300, function() {
                    modal.remove();
                });
            }
        });
    }

    /**
     * Utility functions
     */
    window.MyLunaChatCore = {
        showFeature: showFeatureContent,
        updateUsage: updateUsageWidget,
        showUpgrade: showUpgradeModal,
        canUseAI: canUseAI
    };

})(jQuery);
