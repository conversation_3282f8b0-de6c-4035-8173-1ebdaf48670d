/**
 * MyLunaChat Core JavaScript
 * Handles rewarded ads, feature loading, and UI interactions
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initMyLunaChatCore();
    });

    /**
     * Initialize core functionality
     */
    function initMyLunaChatCore() {
        initRewardedAds();
        initUsageWidget();
        initFormHandlers();
        initEmailFeature();
        initSecurityEnhancements();
    }

    /**
     * Initialize rewarded ad functionality
     */
    function initRewardedAds() {
        // Handle continue button clicks
        window.mylunachatShowFeature = function(instanceId, feature) {
            showFeatureContent(instanceId, feature);
        };
    }

    /**
     * Show feature content after ad viewing
     */
    function showFeatureContent(instanceId, feature) {
        const adContainer = document.getElementById(instanceId + '_ad');
        const contentContainer = document.getElementById(instanceId + '_content');
        
        if (!adContainer || !contentContainer) {
            console.error('MyLunaChat: Container elements not found');
            return;
        }

        // Hide ad container with animation
        $(adContainer).fadeOut(500, function() {
            // Show content container
            $(contentContainer).fadeIn(500);
            
            // Load feature content via AJAX
            loadFeatureContent(instanceId, feature);
        });
    }

    /**
     * Load feature content via AJAX
     */
    function loadFeatureContent(instanceId, feature) {
        const contentContainer = $('#' + instanceId + '_content');
        
        $.ajax({
            url: mylunachat_core_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'mylunachat_load_feature',
                feature: feature,
                security: mylunachat_core_ajax.security || mylunachat_core_ajax.nonce
            },
            beforeSend: function() {
                contentContainer.html(`
                    <div class="loading-message">
                        <div class="cosmic-loader">🌙 ✨ 🌟</div>
                        <p>Consulting the cosmos...</p>
                    </div>
                `);
            },
            success: function(response) {
                if (response.success) {
                    contentContainer.html(response.data.content);
                    
                    // Update usage widget if present
                    updateUsageWidget(response.data.remaining_uses);
                    
                    // Trigger custom event
                    $(document).trigger('mylunachat:feature_loaded', {
                        feature: feature,
                        remaining_uses: response.data.remaining_uses
                    });
                } else {
                    showError(contentContainer, response.data || 'Failed to load content');
                }
            },
            error: function() {
                showError(contentContainer, 'Connection error. Please try again.');
            }
        });
    }

    /**
     * Show error message
     */
    function showError(container, message) {
        container.html(`
            <div class="error-message">
                <div class="error-icon">⚠️</div>
                <p>${message}</p>
                <button class="btn btn-secondary" onclick="location.reload()">Try Again</button>
            </div>
        `);
    }

    /**
     * Initialize usage widget functionality
     */
    function initUsageWidget() {
        // Auto-refresh usage widget every 5 minutes
        setInterval(function() {
            refreshUsageWidget();
        }, 300000); // 5 minutes

        // Listen for usage updates
        $(document).on('mylunachat:usage_updated', function(event, data) {
            updateUsageWidget(data.remaining_uses);
        });
    }

    /**
     * Refresh usage widget
     */
    function refreshUsageWidget() {
        const widgets = $('.mylunachat-usage-widget');
        
        widgets.each(function() {
            const widget = $(this);
            
            $.ajax({
                url: mylunachat_core_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'mylunachat_get_usage_stats',
                    nonce: mylunachat_core_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        updateUsageWidgetDisplay(widget, response.data);
                    }
                }
            });
        });
    }

    /**
     * Update usage widget display
     */
    function updateUsageWidget(remainingUses) {
        const usageCount = $('.usage-count');
        const usageProgress = $('.usage-progress');
        const usageRemaining = $('.usage-remaining');
        
        if (remainingUses !== 'unlimited') {
            const freeLimit = 3; // Should match PHP constant
            const used = freeLimit - remainingUses;
            const percentage = (used / freeLimit) * 100;
            
            usageCount.text(used + '/' + freeLimit);
            usageProgress.css('width', Math.min(100, percentage) + '%');
            
            if (remainingUses > 0) {
                usageRemaining.text(remainingUses + ' consultations remaining today');
            } else {
                usageRemaining.text('Daily limit reached. Resets at midnight.');
            }
        }
    }

    /**
     * Update usage widget with full data
     */
    function updateUsageWidgetDisplay(widget, data) {
        if (data.is_premium) {
            return; // Premium users don't need updates
        }
        
        const usageCount = widget.find('.usage-count');
        const usageProgress = widget.find('.usage-progress');
        const usageRemaining = widget.find('.usage-remaining');
        
        usageCount.text(data.daily_usage + '/' + data.free_limit);
        usageProgress.css('width', Math.min(100, data.percentage_used) + '%');
        
        if (data.remaining_uses > 0) {
            usageRemaining.text(data.remaining_uses + ' consultations remaining today');
        } else {
            usageRemaining.text('Daily limit reached. Resets at midnight.');
        }
    }

    /**
     * Initialize form handlers
     */
    function initFormHandlers() {
        // Handle AI form submissions
        $(document).on('submit', '.mylunachat-ai-form', function(e) {
            e.preventDefault();
            handleAIFormSubmission($(this));
        });

        // Handle premium upgrade prompts
        $(document).on('click', '.mylunachat-upgrade-prompt', function(e) {
            e.preventDefault();
            showUpgradeModal();
        });
    }

    /**
     * Handle AI form submission
     */
    function handleAIFormSubmission(form) {
        const submitBtn = form.find('button[type="submit"]');
        const originalText = submitBtn.text();
        
        // Check if user can use AI
        if (!canUseAI()) {
            showUpgradeModal();
            return;
        }
        
        // Disable submit button
        submitBtn.prop('disabled', true).text('Consulting the stars...');
        
        // Get form data
        const formData = form.serialize();
        const action = form.data('action') || 'mylunachat_ai_request';
        
        $.ajax({
            url: mylunachat_core_ajax.ajax_url,
            type: 'POST',
            data: formData + '&action=' + action + '&nonce=' + mylunachat_core_ajax.nonce,
            success: function(response) {
                if (response.success) {
                    displayAIResponse(form, response.data);
                    
                    // Update usage
                    updateUsageWidget(response.data.remaining_uses);
                    
                    // Trigger event
                    $(document).trigger('mylunachat:ai_response', response.data);
                } else {
                    showFormError(form, response.data.message || 'Request failed');
                }
            },
            error: function() {
                showFormError(form, 'Connection error. Please try again.');
            },
            complete: function() {
                // Re-enable submit button
                submitBtn.prop('disabled', false).text(originalText);
            }
        });
    }

    /**
     * Display AI response
     */
    function displayAIResponse(form, data) {
        const resultContainer = form.siblings('.ai-result').length ? 
            form.siblings('.ai-result') : 
            $('<div class="ai-result"></div>').insertAfter(form);
        
        resultContainer.html(`
            <div class="ai-response">
                <div class="response-header">
                    <h3>✨ Your Cosmic Insights</h3>
                </div>
                <div class="response-content">
                    ${data.message || data.response || data.content}
                </div>
                ${data.pdf_url ? `<div class="response-actions">
                    <a href="${data.pdf_url}" class="btn btn-premium" target="_blank">
                        Download PDF Report 📄
                    </a>
                </div>` : ''}
            </div>
        `).hide().fadeIn(500);
        
        // Scroll to result
        $('html, body').animate({
            scrollTop: resultContainer.offset().top - 100
        }, 500);
    }

    /**
     * Show form error
     */
    function showFormError(form, message) {
        const errorContainer = form.find('.form-error').length ?
            form.find('.form-error') :
            $('<div class="form-error"></div>').prependTo(form);
        
        errorContainer.html(`
            <div class="error-message">
                <span class="error-icon">⚠️</span>
                ${message}
            </div>
        `).hide().fadeIn(300);
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            errorContainer.fadeOut(300);
        }, 5000);
    }

    /**
     * Check if user can use AI
     */
    function canUseAI() {
        // This should be populated by PHP
        return window.mylunachat_can_use_ai !== false;
    }

    /**
     * Show upgrade modal
     */
    function showUpgradeModal() {
        const modal = $(`
            <div class="mylunachat-modal-overlay">
                <div class="mylunachat-modal">
                    <div class="modal-header">
                        <h2>🌟 Unlock Unlimited Cosmic Insights</h2>
                        <button class="modal-close">&times;</button>
                    </div>
                    <div class="modal-content">
                        <div class="upgrade-benefits">
                            <ul>
                                <li>✨ Unlimited AI consultations</li>
                                <li>📊 Detailed PDF reports</li>
                                <li>🚫 Ad-free experience</li>
                                <li>🌙 Personalized alerts</li>
                                <li>💫 Priority support</li>
                            </ul>
                        </div>
                        <div class="modal-actions">
                            <a href="/pricing/" class="btn btn-premium">
                                Upgrade Now - $10/month
                            </a>
                            <button class="btn btn-secondary modal-close">
                                Maybe Later
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `);
        
        $('body').append(modal);
        modal.fadeIn(300);
        
        // Close modal handlers
        modal.find('.modal-close, .mylunachat-modal-overlay').on('click', function(e) {
            if (e.target === this) {
                modal.fadeOut(300, function() {
                    modal.remove();
                });
            }
        });
    }

    /**
     * Initialize email feature
     */
    function initEmailFeature() {
        // Handle email button clicks
        $(document).on('click', '.mylunachat-email-btn', function(e) {
            e.preventDefault();
            const button = $(this);
            const contentData = button.data('content');
            const feature = button.data('feature');

            showEmailModal(contentData, feature);
        });
    }

    /**
     * Show email modal
     */
    function showEmailModal(contentData, feature) {
        const modal = $(`
            <div class="mylunachat-email-modal-overlay">
                <div class="mylunachat-email-modal">
                    <div class="modal-header">
                        <h3>📧 Email Your Reading</h3>
                        <button class="modal-close">&times;</button>
                    </div>
                    <div class="modal-content">
                        <form id="email-result-form">
                            <div class="form-group">
                                <label for="email_address">Email Address:</label>
                                <input type="email" id="email_address" name="email" required
                                       placeholder="<EMAIL>" class="form-control">
                            </div>

                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="email_optin" name="opt_in" value="1">
                                    <span class="checkmark"></span>
                                    Subscribe to MyLunaChat updates and cosmic insights
                                </label>
                            </div>

                            <div class="modal-actions">
                                <button type="submit" class="btn btn-primary">
                                    Send Reading ✨
                                </button>
                                <button type="button" class="btn btn-secondary modal-close">
                                    Cancel
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `);

        $('body').append(modal);
        modal.fadeIn(300);

        // Focus email input
        modal.find('#email_address').focus();

        // Handle form submission
        modal.find('#email-result-form').on('submit', function(e) {
            e.preventDefault();
            sendEmailResult($(this), contentData, feature, modal);
        });

        // Close modal handlers
        modal.find('.modal-close, .mylunachat-email-modal-overlay').on('click', function(e) {
            if (e.target === this) {
                modal.fadeOut(300, function() {
                    modal.remove();
                });
            }
        });
    }

    /**
     * Send email result
     */
    function sendEmailResult(form, contentData, feature, modal) {
        const submitBtn = form.find('button[type="submit"]');
        const originalText = submitBtn.text();
        const email = form.find('#email_address').val();
        const optIn = form.find('#email_optin').is(':checked');

        submitBtn.prop('disabled', true).text('Sending...');

        $.ajax({
            url: mylunachat_core_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'mylunachat_email_result',
                email: email,
                content_data: contentData,
                opt_in: optIn,
                security: mylunachat_core_ajax.security || mylunachat_core_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showEmailSuccess(modal, response.data.message);
                } else {
                    showEmailError(form, response.data.message);
                }
            },
            error: function() {
                showEmailError(form, 'Connection error. Please try again.');
            },
            complete: function() {
                submitBtn.prop('disabled', false).text(originalText);
            }
        });
    }

    /**
     * Show email success message
     */
    function showEmailSuccess(modal, message) {
        modal.find('.modal-content').html(`
            <div class="email-success">
                <div class="success-icon">✅</div>
                <h3>Email Sent Successfully!</h3>
                <p>${message}</p>
                <button class="btn btn-primary modal-close">Close</button>
            </div>
        `);

        // Auto-close after 3 seconds
        setTimeout(function() {
            modal.fadeOut(300, function() {
                modal.remove();
            });
        }, 3000);
    }

    /**
     * Show email error
     */
    function showEmailError(form, message) {
        let errorDiv = form.find('.email-error');
        if (errorDiv.length === 0) {
            errorDiv = $('<div class="email-error"></div>').prependTo(form);
        }

        errorDiv.html(`
            <div class="error-message">
                <span class="error-icon">⚠️</span>
                ${message}
            </div>
        `).hide().fadeIn(300);

        // Auto-hide after 5 seconds
        setTimeout(function() {
            errorDiv.fadeOut(300);
        }, 5000);
    }

    /**
     * Initialize security enhancements
     */
    function initSecurityEnhancements() {
        // Refresh nonce every 10 minutes
        setInterval(function() {
            refreshSecurityNonce();
        }, 600000); // 10 minutes

        // Add nonce to all AJAX requests
        $(document).ajaxSend(function(event, xhr, settings) {
            if (settings.url === mylunachat_core_ajax.ajax_url) {
                // Ensure security parameter is included
                if (settings.data && settings.data.indexOf('security=') === -1) {
                    const separator = settings.data ? '&' : '';
                    settings.data += separator + 'security=' + (mylunachat_core_ajax.security || mylunachat_core_ajax.nonce);
                }
            }
        });
    }

    /**
     * Refresh security nonce
     */
    function refreshSecurityNonce() {
        $.ajax({
            url: mylunachat_core_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'mylunachat_refresh_nonce',
                security: mylunachat_core_ajax.security || mylunachat_core_ajax.nonce
            },
            success: function(response) {
                if (response.success && response.data.nonce) {
                    mylunachat_core_ajax.security = response.data.nonce;
                    mylunachat_core_ajax.nonce = response.data.nonce;
                }
            }
        });
    }

    /**
     * Enhanced utility functions
     */
    window.MyLunaChatCore = {
        showFeature: showFeatureContent,
        updateUsage: updateUsageWidget,
        showUpgrade: showUpgradeModal,
        canUseAI: canUseAI,
        showEmailModal: showEmailModal,
        refreshNonce: refreshSecurityNonce
    };

})(jQuery);
