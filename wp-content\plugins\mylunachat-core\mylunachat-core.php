<?php
/**
 * Plugin Name: MyLunaChat Core Functions
 * Plugin URI: https://mylunachat.com
 * Description: Core modular functions and shortcodes for MyLunaChat.com astrology platform
 * Version: 1.0.0
 * Author: MyLunaChat Development Team
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('MYLUNACHAT_CORE_VERSION', '1.0.0');
define('MYLUNACHAT_CORE_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('MYLUNACHAT_CORE_PLUGIN_URL', plugin_dir_url(__FILE__));

/**
 * ========================================
 * 🔹 1. AI USAGE ENFORCEMENT FUNCTIONS
 * ========================================
 */

/**
 * Check if user can use AI features
 * Returns true if user is premium OR has used less than 3 free AI interactions today
 * 
 * @param int $user_id User ID (optional, defaults to current user)
 * @return bool
 */
function mylunachat_can_use_ai($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if (!$user_id) {
        return false; // Not logged in
    }
    
    // Premium users have unlimited access
    if (mylunachat_is_premium_user($user_id)) {
        return true;
    }
    
    // Free users have daily limit
    $daily_usage = mylunachat_get_daily_ai_usage($user_id);
    $free_limit = defined('MYLUNACHAT_FREE_AI_LIMIT') ? MYLUNACHAT_FREE_AI_LIMIT : 3;
    
    return $daily_usage < $free_limit;
}

/**
 * Get user's daily AI usage count
 * Uses date-based key for automatic daily reset
 * 
 * @param int $user_id User ID (optional, defaults to current user)
 * @return int Usage count for today
 */
function mylunachat_get_daily_ai_usage($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if (!$user_id) {
        return 0;
    }
    
    $today = date('Y-m-d');
    $usage_key = 'mylunachat_ai_usage_' . $today;
    
    return (int) get_user_meta($user_id, $usage_key, true);
}

/**
 * Increment user's daily AI usage
 * Automatically creates new counter for new days
 * 
 * @param int $user_id User ID (optional, defaults to current user)
 * @return bool Success status
 */
function mylunachat_increment_ai_usage($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if (!$user_id) {
        return false;
    }
    
    $today = date('Y-m-d');
    $usage_key = 'mylunachat_ai_usage_' . $today;
    $current_usage = mylunachat_get_daily_ai_usage($user_id);
    
    // Increment usage
    $new_usage = $current_usage + 1;
    update_user_meta($user_id, $usage_key, $new_usage);
    
    // Clean up old usage data (keep only last 7 days)
    mylunachat_cleanup_old_usage_data($user_id);
    
    return true;
}

/**
 * Clean up old AI usage data (keep only last 7 days)
 * 
 * @param int $user_id User ID
 */
function mylunachat_cleanup_old_usage_data($user_id) {
    global $wpdb;
    
    // Get all usage meta keys for this user
    $meta_keys = $wpdb->get_col($wpdb->prepare(
        "SELECT meta_key FROM {$wpdb->usermeta} 
         WHERE user_id = %d 
         AND meta_key LIKE 'mylunachat_ai_usage_%%'",
        $user_id
    ));
    
    $cutoff_date = date('Y-m-d', strtotime('-7 days'));
    
    foreach ($meta_keys as $meta_key) {
        // Extract date from key
        $date_part = str_replace('mylunachat_ai_usage_', '', $meta_key);
        
        // Delete if older than 7 days
        if ($date_part < $cutoff_date) {
            delete_user_meta($user_id, $meta_key);
        }
    }
}

/**
 * Get remaining AI uses for today
 * 
 * @param int $user_id User ID (optional, defaults to current user)
 * @return int|string Remaining uses or "unlimited" for premium users
 */
function mylunachat_get_remaining_ai_uses($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if (mylunachat_is_premium_user($user_id)) {
        return 'unlimited';
    }
    
    $free_limit = defined('MYLUNACHAT_FREE_AI_LIMIT') ? MYLUNACHAT_FREE_AI_LIMIT : 3;
    $used = mylunachat_get_daily_ai_usage($user_id);
    
    return max(0, $free_limit - $used);
}

/**
 * Check if user is premium member
 * This function should already exist, but including for completeness
 * 
 * @param int $user_id User ID (optional, defaults to current user)
 * @return bool
 */
if (!function_exists('mylunachat_is_premium_user')) {
    function mylunachat_is_premium_user($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        if (!$user_id) {
            return false;
        }
        
        // Check MemberPress membership status
        if (function_exists('mepr_get_user_active_memberships')) {
            $memberships = mepr_get_user_active_memberships($user_id);
            return !empty($memberships);
        }
        
        // Fallback: check custom meta
        return get_user_meta($user_id, 'mylunachat_premium', true) === 'active';
    }
}

/**
 * ========================================
 * 🔹 2. AI WRAPPER CLASS
 * ========================================
 */

/**
 * MyLunaChat AI Wrapper Class
 * Handles all AI interactions with context and usage tracking
 */
class MylunaChatAI {
    
    private static $instance = null;
    private $api_key;
    private $model;
    private $temperature;
    
    /**
     * Singleton pattern
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->api_key = get_option('mylunachat_ai_api_key', '');
        $this->model = get_option('mylunachat_ai_model', 'gpt-3.5-turbo');
        $this->temperature = get_option('mylunachat_ai_temperature', 0.7);
    }
    
    /**
     * Get AI response with context and usage tracking
     * Enhanced with moon phase integration for birth charts
     *
     * @param string $prompt User's prompt/question
     * @param string $context Context type (birth_chart, dream_interpreter, compatibility, etc.)
     * @param int $user_id User ID for usage tracking
     * @param array $metadata Additional metadata (birth_date, birth_location, etc.)
     * @return array Response array with success status and data
     */
    public static function get_response($prompt, $context = 'general', $user_id = null, $metadata = array()) {
        $instance = self::getInstance();

        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        // Check if user can use AI
        if (!mylunachat_can_use_ai($user_id)) {
            return array(
                'success' => false,
                'error' => 'daily_limit_reached',
                'message' => 'Daily AI limit reached. <a href="' . mylunachat_get_upgrade_link('ai_limit', 'daily_limit_reached') . '">Upgrade to Premium</a> for unlimited access.',
                'remaining_uses' => 0,
                'upgrade_url' => mylunachat_get_upgrade_link('ai_limit', 'daily_limit_reached')
            );
        }

        // Validate API key
        if (empty($instance->api_key)) {
            return array(
                'success' => false,
                'error' => 'api_not_configured',
                'message' => 'AI service is not configured. Please contact support.'
            );
        }

        // Get context-specific system prompt
        $system_prompt = $instance->get_system_prompt($context);

        // Enhance prompt with moon phase for birth charts
        if ($context === 'birth_chart' && isset($metadata['birth_date'])) {
            $birth_location = isset($metadata['birth_location']) ? $metadata['birth_location'] : '';
            $prompt = mylunachat_enhance_birth_chart_with_moon_phase($prompt, $metadata['birth_date'], $birth_location);
        }

        // Make API call
        $response = $instance->call_openai_api($prompt, $system_prompt);

        if ($response['success']) {
            // Increment usage for successful calls
            mylunachat_increment_ai_usage($user_id);

            // Save conversation history with metadata
            $instance->save_conversation($user_id, $prompt, $response['data'], $context, $metadata);

            // Add remaining uses to response
            $response['remaining_uses'] = mylunachat_get_remaining_ai_uses($user_id);

            // Add email button to response
            $response['email_button'] = mylunachat_generate_email_button($response['data'], $context, $metadata);
        }

        return $response;
    }
    
    /**
     * Get system prompt based on context
     * 
     * @param string $context Context type
     * @return string System prompt
     */
    private function get_system_prompt($context) {
        $prompts = array(
            'birth_chart' => "You are a professional astrologer with deep knowledge of natal chart interpretation. Analyze birth charts with insights about Sun, Moon, Rising signs, planetary positions, house placements, and major aspects. Use a warm, encouraging, and mystical tone while providing practical guidance.",
            
            'dream_interpreter' => "You are a Jungian dream analyst and spiritual guide with expertise in dream symbolism, archetypes, and astrological connections. Explore symbolic meanings, emotional context, connections to waking life, and provide spiritual and psychological insights with compassion.",
            
            'compatibility' => "You are an expert relationship astrologer specializing in synastry and compatibility analysis. Compare sun, moon, rising signs, analyze Venus and Mars placements, look at communication styles, and provide balanced relationship advice highlighting both harmonious aspects and growth areas.",
            
            'horoscope' => "You are a skilled astrologer creating personalized horoscopes. Provide general energy themes, love and relationship insights, career and financial guidance, health advice, spiritual growth opportunities, and lucky elements. Keep the tone uplifting and empowering.",
            
            'general' => "You are an intuitive AI astrologer and spiritual guide with vast astrological knowledge. Provide personalized insights that are warm, compassionate, spiritually insightful yet practical, encouraging, and based on astrological principles."
        );
        
        return isset($prompts[$context]) ? $prompts[$context] : $prompts['general'];
    }

    /**
     * Call OpenAI API
     *
     * @param string $prompt User prompt
     * @param string $system_prompt System prompt
     * @return array Response array
     */
    private function call_openai_api($prompt, $system_prompt) {
        $data = array(
            'model' => $this->model,
            'messages' => array(
                array(
                    'role' => 'system',
                    'content' => $system_prompt
                ),
                array(
                    'role' => 'user',
                    'content' => $prompt
                )
            ),
            'temperature' => $this->temperature,
            'max_tokens' => 1000
        );

        $response = wp_remote_post('https://api.openai.com/v1/chat/completions', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $this->api_key,
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode($data),
            'timeout' => 30
        ));

        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'error' => 'api_error',
                'message' => 'Unable to connect to AI service. Please try again.'
            );
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (isset($data['choices'][0]['message']['content'])) {
            return array(
                'success' => true,
                'data' => trim($data['choices'][0]['message']['content']),
                'usage' => isset($data['usage']) ? $data['usage'] : null
            );
        }

        return array(
            'success' => false,
            'error' => 'invalid_response',
            'message' => 'Invalid response from AI service.'
        );
    }

    /**
     * Save conversation history
     *
     * @param int $user_id User ID
     * @param string $prompt User prompt
     * @param string $response AI response
     * @param string $context Context type
     * @param array $metadata Additional metadata
     */
    private function save_conversation($user_id, $prompt, $response, $context, $metadata = array()) {
        $conversation_data = array(
            'user_id' => $user_id,
            'prompt' => $prompt,
            'response' => $response,
            'context' => $context,
            'metadata' => $metadata,
            'timestamp' => current_time('mysql'),
            'date' => date('Y-m-d')
        );

        // Get existing conversations
        $conversations = get_user_meta($user_id, 'mylunachat_ai_conversations', true);
        if (!is_array($conversations)) {
            $conversations = array();
        }

        // Add new conversation
        $conversations[] = $conversation_data;

        // Keep only last 50 conversations
        if (count($conversations) > 50) {
            $conversations = array_slice($conversations, -50);
        }

        update_user_meta($user_id, 'mylunachat_ai_conversations', $conversations);
    }

    /**
     * Get user's conversation history
     *
     * @param int $user_id User ID
     * @param int $limit Number of conversations to return
     * @return array Conversation history
     */
    public static function get_conversation_history($user_id = null, $limit = 10) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        $conversations = get_user_meta($user_id, 'mylunachat_ai_conversations', true);
        if (!is_array($conversations)) {
            return array();
        }

        // Return most recent conversations
        return array_slice(array_reverse($conversations), 0, $limit);
    }
}

/**
 * ========================================
 * 🔹 3. REWARDED AD SHORTCODE SYSTEM
 * ========================================
 */

/**
 * Rewarded AI Shortcode
 * Shows AdSense ad for free users, premium users skip directly to content
 *
 * Usage: [mylunachat_rewarded_ai feature="dream_interpreter" delay="15"]
 *
 * @param array $atts Shortcode attributes
 * @return string HTML output
 */
function mylunachat_rewarded_ai_shortcode($atts) {
    $atts = shortcode_atts(array(
        'feature' => 'dream_interpreter',
        'delay' => '15',
        'ad_client' => get_option('mylunachat_adsense_client', 'ca-pub-XXXXXXXXXX'),
        'ad_slot' => get_option('mylunachat_adsense_slot', 'XXXXXXXXXX'),
        'premium_message' => 'Welcome back, Premium Member! 🌟'
    ), $atts);

    $user_id = get_current_user_id();
    $is_premium = mylunachat_is_premium_user($user_id);

    // Generate unique ID for this instance
    $instance_id = 'mylunachat_rewarded_' . uniqid();

    ob_start();
    ?>
    <div id="<?php echo esc_attr($instance_id); ?>" class="mylunachat-rewarded-container">

        <?php if ($is_premium): ?>
            <!-- Premium User - Skip Ad -->
            <div class="mylunachat-premium-welcome">
                <div class="premium-badge">
                    <span class="premium-icon">👑</span>
                    <?php echo esc_html($atts['premium_message']); ?>
                </div>
            </div>
            <div class="mylunachat-feature-content">
                <?php echo mylunachat_render_feature_content($atts['feature']); ?>
            </div>

        <?php else: ?>
            <!-- Free User - Show Rewarded Ad -->
            <div class="mylunachat-ad-container" id="<?php echo esc_attr($instance_id); ?>_ad">
                <div class="ad-header">
                    <h3>🌙 Support MyLunaChat</h3>
                    <p>Watch this ad to unlock your cosmic insights! ✨</p>
                    <div class="ad-timer">
                        <span id="<?php echo esc_attr($instance_id); ?>_timer"><?php echo esc_attr($atts['delay']); ?></span> seconds remaining
                    </div>
                </div>

                <!-- AdSense Ad -->
                <div class="adsense-container">
                    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=<?php echo esc_attr($atts['ad_client']); ?>" crossorigin="anonymous"></script>
                    <ins class="adsbygoogle"
                         style="display:block"
                         data-ad-client="<?php echo esc_attr($atts['ad_client']); ?>"
                         data-ad-slot="<?php echo esc_attr($atts['ad_slot']); ?>"
                         data-ad-format="auto"
                         data-full-width-responsive="true"></ins>
                    <script>
                         (adsbygoogle = window.adsbygoogle || []).push({});
                    </script>
                </div>

                <!-- Continue Button (hidden initially) -->
                <div class="continue-section" id="<?php echo esc_attr($instance_id); ?>_continue" style="display: none;">
                    <button class="btn btn-primary mylunachat-continue-btn" onclick="mylunachatShowFeature('<?php echo esc_attr($instance_id); ?>', '<?php echo esc_attr($atts['feature']); ?>')">
                        Continue to Your Reading ✨
                    </button>
                </div>
            </div>

            <!-- Feature Content (hidden initially) -->
            <div class="mylunachat-feature-content" id="<?php echo esc_attr($instance_id); ?>_content" style="display: none;">
                <div class="loading-message">
                    <div class="cosmic-loader">🌙 ✨ 🌟</div>
                    <p>Preparing your cosmic insights...</p>
                </div>
            </div>

        <?php endif; ?>

    </div>

    <?php if (!$is_premium): ?>
    <script>
    // Timer countdown
    let <?php echo esc_attr($instance_id); ?>_timeLeft = <?php echo intval($atts['delay']); ?>;
    let <?php echo esc_attr($instance_id); ?>_timer = setInterval(function() {
        <?php echo esc_attr($instance_id); ?>_timeLeft--;
        document.getElementById('<?php echo esc_attr($instance_id); ?>_timer').textContent = <?php echo esc_attr($instance_id); ?>_timeLeft;

        if (<?php echo esc_attr($instance_id); ?>_timeLeft <= 0) {
            clearInterval(<?php echo esc_attr($instance_id); ?>_timer);
            document.getElementById('<?php echo esc_attr($instance_id); ?>_continue').style.display = 'block';
            document.querySelector('#<?php echo esc_attr($instance_id); ?> .ad-timer').innerHTML = '<span class="ready-indicator">✅ Ready!</span>';
        }
    }, 1000);
    </script>
    <?php endif; ?>

    <?php
    return ob_get_clean();
}

/**
 * Render feature content based on feature type
 *
 * @param string $feature Feature type
 * @return string HTML content
 */
function mylunachat_render_feature_content($feature) {
    switch ($feature) {
        case 'dream_interpreter':
            return do_shortcode('[mylunachat_dream_interpreter]');

        case 'birth_chart':
            return do_shortcode('[mylunachat_birth_chart]');

        case 'compatibility':
            return do_shortcode('[mylunachat_compatibility]');

        case 'ai_chat':
            return do_shortcode('[mylunachat_ai_chat]');

        case 'horoscope':
            return do_shortcode('[mylunachat_horoscope type="daily"]');

        default:
            return '<p>Feature not available. Please contact support.</p>';
    }
}

/**
 * ========================================
 * 🔐 SECURITY & NONCE VERIFICATION
 * ========================================
 */

/**
 * Helper function to enqueue AJAX nonce for JavaScript
 * Prints nonce to page for AJAX security
 */
function mylunachat_enqueue_ajax_nonce() {
    $nonce = wp_create_nonce('mylunachat_nonce');
    echo "<script type='text/javascript'>
        window.mylunachat_ajax_nonce = '{$nonce}';
        window.mylunachat_ajax_url = '" . admin_url('admin-ajax.php') . "';
    </script>";
}

/**
 * Verify AJAX nonce and return standardized error if invalid
 *
 * @param string $nonce_field Field name containing nonce (default: 'security')
 * @return bool True if valid, sends JSON error and dies if invalid
 */
function mylunachat_verify_ajax_nonce($nonce_field = 'security') {
    if (!check_ajax_referer('mylunachat_nonce', $nonce_field, false)) {
        wp_send_json_error(array(
            'message' => 'Security verification failed. Please refresh the page and try again.',
            'error_code' => 'invalid_nonce'
        ));
        wp_die();
    }
    return true;
}

/**
 * Enhanced AJAX handler for loading feature content
 */
function mylunachat_ajax_load_feature() {
    mylunachat_verify_ajax_nonce();

    $feature = sanitize_text_field($_POST['feature']);
    $user_id = get_current_user_id();

    if (!$user_id) {
        wp_send_json_error('Please log in to continue.');
    }

    // Check if user can access this feature
    if (!mylunachat_can_use_ai($user_id) && !mylunachat_is_premium_user($user_id)) {
        wp_send_json_error('Daily limit reached. Please upgrade to Premium.');
    }

    $content = mylunachat_render_feature_content($feature);

    wp_send_json_success(array(
        'content' => $content,
        'remaining_uses' => mylunachat_get_remaining_ai_uses($user_id)
    ));
}

/**
 * ========================================
 * 🔹 4. SHORTCODE REGISTRATIONS & HOOKS
 * ========================================
 */

// Register shortcodes
add_shortcode('mylunachat_rewarded_ai', 'mylunachat_rewarded_ai_shortcode');

// Register AJAX handlers
add_action('wp_ajax_mylunachat_load_feature', 'mylunachat_ajax_load_feature');
add_action('wp_ajax_nopriv_mylunachat_load_feature', 'mylunachat_ajax_load_feature');
add_action('wp_ajax_mylunachat_email_result', 'mylunachat_ajax_email_result');
add_action('wp_ajax_nopriv_mylunachat_email_result', 'mylunachat_ajax_email_result');
add_action('wp_ajax_mylunachat_refresh_nonce', 'mylunachat_ajax_refresh_nonce');
add_action('wp_ajax_nopriv_mylunachat_refresh_nonce', 'mylunachat_ajax_refresh_nonce');

/**
 * AJAX handler for nonce refresh
 */
function mylunachat_ajax_refresh_nonce() {
    mylunachat_verify_ajax_nonce();

    $new_nonce = wp_create_nonce('mylunachat_nonce');

    wp_send_json_success(array(
        'nonce' => $new_nonce,
        'timestamp' => current_time('timestamp')
    ));
}

/**
 * Enqueue scripts and styles
 */
function mylunachat_core_enqueue_scripts() {
    wp_enqueue_script('mylunachat-core', MYLUNACHAT_CORE_PLUGIN_URL . 'js/mylunachat-core.js', array('jquery'), MYLUNACHAT_CORE_VERSION, true);
    wp_enqueue_style('mylunachat-core', MYLUNACHAT_CORE_PLUGIN_URL . 'css/mylunachat-core.css', array(), MYLUNACHAT_CORE_VERSION);

    wp_localize_script('mylunachat-core', 'mylunachat_core_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('mylunachat_nonce'),
        'security' => wp_create_nonce('mylunachat_nonce') // Alternative field name
    ));
}
add_action('wp_enqueue_scripts', 'mylunachat_core_enqueue_scripts');

/**
 * Print nonce to footer for additional security
 */
function mylunachat_print_ajax_nonce() {
    if (is_user_logged_in()) {
        mylunachat_enqueue_ajax_nonce();
    }
}
add_action('wp_footer', 'mylunachat_print_ajax_nonce');

/**
 * Capture UTM parameters on page load
 */
function mylunachat_capture_utm_on_load() {
    $utm_params = array();
    $utm_keys = array('utm_source', 'utm_medium', 'utm_campaign', 'utm_content', 'utm_term');

    foreach ($utm_keys as $key) {
        if (isset($_GET[$key])) {
            $utm_params[$key] = sanitize_text_field($_GET[$key]);
        }
    }

    if (!empty($utm_params)) {
        mylunachat_store_utm_attribution($utm_params);
    }
}
add_action('init', 'mylunachat_capture_utm_on_load');

/**
 * ========================================
 * 📤 EMAIL MY RESULT FEATURE
 * ========================================
 */

/**
 * Generate "Email My Result" button HTML
 *
 * @param string $content AI result content
 * @param string $feature Feature type (dream_interpreter, birth_chart, etc.)
 * @param array $metadata Additional metadata for the reading
 * @return string HTML button
 */
function mylunachat_generate_email_button($content, $feature, $metadata = array()) {
    if (!is_user_logged_in()) {
        return ''; // Only show for logged-in users
    }

    $button_id = 'email-result-' . uniqid();
    $encoded_content = base64_encode(json_encode(array(
        'content' => $content,
        'feature' => $feature,
        'metadata' => $metadata,
        'timestamp' => current_time('mysql')
    )));

    return sprintf(
        '<div class="mylunachat-email-section">
            <button class="btn btn-secondary mylunachat-email-btn"
                    id="%s"
                    data-content="%s"
                    data-feature="%s">
                📧 Email This Reading
            </button>
        </div>',
        esc_attr($button_id),
        esc_attr($encoded_content),
        esc_attr($feature)
    );
}

/**
 * AJAX handler for email result request
 */
function mylunachat_ajax_email_result() {
    mylunachat_verify_ajax_nonce();

    $user_id = get_current_user_id();
    if (!$user_id) {
        wp_send_json_error(array('message' => 'Please log in to email results.'));
    }

    $email = sanitize_email($_POST['email']);
    $content_data = json_decode(base64_decode($_POST['content_data']), true);
    $opt_in = isset($_POST['opt_in']) ? (bool) $_POST['opt_in'] : false;

    if (!is_email($email)) {
        wp_send_json_error(array('message' => 'Please enter a valid email address.'));
    }

    if (!$content_data || !isset($content_data['content'])) {
        wp_send_json_error(array('message' => 'Invalid content data.'));
    }

    // Send email
    $email_sent = mylunachat_send_result_email($email, $content_data, $user_id);

    if ($email_sent) {
        // Track email opt-in if requested
        if ($opt_in) {
            mylunachat_track_email_optin($user_id, $email);
        }

        // Track email send event
        mylunachat_track_email_send($user_id, $email, $content_data['feature']);

        wp_send_json_success(array(
            'message' => 'Your reading has been sent to ' . $email . ' ✨'
        ));
    } else {
        wp_send_json_error(array(
            'message' => 'Failed to send email. Please try again.'
        ));
    }
}

/**
 * Send result email to user
 *
 * @param string $email Recipient email
 * @param array $content_data Content and metadata
 * @param int $user_id User ID
 * @return bool Success status
 */
function mylunachat_send_result_email($email, $content_data, $user_id) {
    $user = get_user_by('ID', $user_id);
    $user_name = $user ? $user->display_name : 'Cosmic Seeker';

    $feature_names = array(
        'dream_interpreter' => 'Dream Interpretation',
        'birth_chart' => 'Birth Chart Analysis',
        'compatibility' => 'Compatibility Reading',
        'ai_chat' => 'AI Consultation',
        'horoscope' => 'Horoscope Reading'
    );

    $feature_name = isset($feature_names[$content_data['feature']])
        ? $feature_names[$content_data['feature']]
        : 'Cosmic Reading';

    $subject = "🌙 Your {$feature_name} from MyLunaChat.com";

    $message = mylunachat_format_email_content($user_name, $feature_name, $content_data);

    $headers = array(
        'Content-Type: text/html; charset=UTF-8',
        'From: MyLunaChat <<EMAIL>>'
    );

    return wp_mail($email, $subject, $message, $headers);
}

/**
 * Format email content with nice HTML template
 *
 * @param string $user_name User's display name
 * @param string $feature_name Feature display name
 * @param array $content_data Content and metadata
 * @return string Formatted HTML email
 */
function mylunachat_format_email_content($user_name, $feature_name, $content_data) {
    $content = $content_data['content'];
    $timestamp = isset($content_data['timestamp']) ? $content_data['timestamp'] : current_time('mysql');
    $formatted_date = date('F j, Y \a\t g:i A', strtotime($timestamp));

    return "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <title>Your {$feature_name}</title>
        <style>
            body { font-family: 'Georgia', serif; line-height: 1.6; color: #333; background: #f8f9fa; margin: 0; padding: 20px; }
            .container { max-width: 600px; margin: 0 auto; background: #fff; border-radius: 12px; overflow: hidden; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
            .header { background: linear-gradient(135deg, #0A0F29, #1a1f3a); color: #FFD700; padding: 30px; text-align: center; }
            .header h1 { margin: 0; font-size: 24px; }
            .content { padding: 30px; }
            .reading-content { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #30D5C8; margin: 20px 0; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 14px; color: #666; }
            .footer a { color: #30D5C8; text-decoration: none; }
            .metadata { font-size: 14px; color: #666; margin-bottom: 15px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>🌙 Your {$feature_name}</h1>
                <p>From MyLunaChat.com</p>
            </div>

            <div class='content'>
                <p>Dear {$user_name},</p>

                <p>Here is your personalized {$feature_name} as requested:</p>

                <div class='metadata'>
                    <strong>Reading Type:</strong> {$feature_name}<br>
                    <strong>Generated:</strong> {$formatted_date}
                </div>

                <div class='reading-content'>
                    " . nl2br(esc_html($content)) . "
                </div>

                <p>Thank you for using MyLunaChat.com for your cosmic guidance! ✨</p>

                <p>Want more insights? <a href='" . home_url() . "'>Visit MyLunaChat.com</a> for unlimited readings with our Premium membership.</p>
            </div>

            <div class='footer'>
                <p>This reading was generated by MyLunaChat.com AI Astrology Platform</p>
                <p><a href='" . home_url() . "'>MyLunaChat.com</a> | <a href='" . home_url('/privacy/') . "'>Privacy Policy</a></p>
            </div>
        </div>
    </body>
    </html>";
}

/**
 * Track email opt-in for mailing list
 *
 * @param int $user_id User ID
 * @param string $email Email address
 */
function mylunachat_track_email_optin($user_id, $email) {
    $existing_emails = get_user_meta($user_id, 'mylunachat_optin_emails', true);
    if (!is_array($existing_emails)) {
        $existing_emails = array();
    }

    if (!in_array($email, $existing_emails)) {
        $existing_emails[] = $email;
        update_user_meta($user_id, 'mylunachat_optin_emails', $existing_emails);
    }

    // Update global opt-in status
    update_user_meta($user_id, 'mylunachat_email_optin', 'yes');
    update_user_meta($user_id, 'mylunachat_email_optin_date', current_time('mysql'));
}

/**
 * Track email send event for analytics
 *
 * @param int $user_id User ID
 * @param string $email Email address
 * @param string $feature Feature type
 */
function mylunachat_track_email_send($user_id, $email, $feature) {
    $email_history = get_user_meta($user_id, 'mylunachat_email_history', true);
    if (!is_array($email_history)) {
        $email_history = array();
    }

    $email_history[] = array(
        'email' => $email,
        'feature' => $feature,
        'timestamp' => current_time('mysql'),
        'date' => date('Y-m-d')
    );

    // Keep only last 50 email sends
    if (count($email_history) > 50) {
        $email_history = array_slice($email_history, -50);
    }

    update_user_meta($user_id, 'mylunachat_email_history', $email_history);
}

/**
 * ========================================
 * 📅 MOON PHASE INTEGRATION
 * ========================================
 */

/**
 * Get moon phase for a specific date and location
 *
 * @param string $date Date in Y-m-d format
 * @param string $location Location string (optional, for future enhancement)
 * @return array Moon phase data
 */
function mylunachat_get_moon_phase($date, $location = '') {
    $timestamp = strtotime($date);
    if (!$timestamp) {
        return array(
            'phase' => 'Unknown',
            'illumination' => 0,
            'description' => 'Unable to calculate moon phase'
        );
    }

    // Calculate moon phase using astronomical algorithm
    $moon_data = mylunachat_calculate_moon_phase($timestamp);

    return array(
        'phase' => $moon_data['phase_name'],
        'illumination' => $moon_data['illumination'],
        'description' => $moon_data['description'],
        'emoji' => $moon_data['emoji'],
        'influence' => $moon_data['astrological_influence']
    );
}

/**
 * Calculate moon phase using astronomical algorithm
 * Based on simplified lunar phase calculation
 *
 * @param int $timestamp Unix timestamp
 * @return array Moon phase data
 */
function mylunachat_calculate_moon_phase($timestamp) {
    // Known new moon reference: January 6, 2000, 18:14 UTC
    $known_new_moon = mktime(18, 14, 0, 1, 6, 2000);

    // Lunar cycle is approximately 29.53059 days
    $lunar_cycle = 29.53059;

    // Calculate days since known new moon
    $days_since = ($timestamp - $known_new_moon) / (24 * 60 * 60);

    // Calculate current position in lunar cycle
    $cycle_position = fmod($days_since, $lunar_cycle);
    if ($cycle_position < 0) {
        $cycle_position += $lunar_cycle;
    }

    // Calculate illumination percentage
    $illumination = (1 - cos(2 * pi() * $cycle_position / $lunar_cycle)) / 2;
    $illumination_percent = round($illumination * 100);

    // Determine phase name and details
    if ($cycle_position < 1.84566) {
        $phase = array(
            'phase_name' => 'New Moon',
            'emoji' => '🌑',
            'description' => 'The moon is not visible, marking new beginnings and fresh starts.',
            'astrological_influence' => 'Perfect time for setting intentions, starting new projects, and planting seeds for the future.'
        );
    } elseif ($cycle_position < 5.53699) {
        $phase = array(
            'phase_name' => 'Waxing Crescent',
            'emoji' => '🌒',
            'description' => 'A sliver of moon is visible, growing larger each night.',
            'astrological_influence' => 'Time for taking action on intentions, building momentum, and overcoming obstacles.'
        );
    } elseif ($cycle_position < 9.22831) {
        $phase = array(
            'phase_name' => 'First Quarter',
            'emoji' => '🌓',
            'description' => 'Half of the moon is illuminated, representing decision and action.',
            'astrological_influence' => 'Period of challenges and decisions, requiring courage and determination to move forward.'
        );
    } elseif ($cycle_position < 12.91963) {
        $phase = array(
            'phase_name' => 'Waxing Gibbous',
            'emoji' => '🌔',
            'description' => 'The moon is nearly full, building energy and anticipation.',
            'astrological_influence' => 'Time for refinement, adjustment, and preparing for manifestation of goals.'
        );
    } elseif ($cycle_position < 16.61096) {
        $phase = array(
            'phase_name' => 'Full Moon',
            'emoji' => '🌕',
            'description' => 'The moon is fully illuminated, representing culmination and completion.',
            'astrological_influence' => 'Peak energy for manifestation, celebration, and releasing what no longer serves you.'
        );
    } elseif ($cycle_position < 20.30228) {
        $phase = array(
            'phase_name' => 'Waning Gibbous',
            'emoji' => '🌖',
            'description' => 'The moon begins to decrease, encouraging gratitude and sharing.',
            'astrological_influence' => 'Time for gratitude, sharing wisdom, and beginning to release and let go.'
        );
    } elseif ($cycle_position < 23.99361) {
        $phase = array(
            'phase_name' => 'Last Quarter',
            'emoji' => '🌗',
            'description' => 'Half of the moon is illuminated, representing release and forgiveness.',
            'astrological_influence' => 'Period of release, forgiveness, and breaking free from limiting patterns.'
        );
    } else {
        $phase = array(
            'phase_name' => 'Waning Crescent',
            'emoji' => '🌘',
            'description' => 'A small crescent remains, encouraging rest and reflection.',
            'astrological_influence' => 'Time for rest, reflection, meditation, and preparing for the next cycle.'
        );
    }

    $phase['illumination'] = $illumination_percent;

    return $phase;
}

/**
 * Enhance birth chart prompt with moon phase information
 *
 * @param string $original_prompt Original AI prompt
 * @param string $birth_date Birth date in Y-m-d format
 * @param string $birth_location Birth location (optional)
 * @return string Enhanced prompt with moon phase
 */
function mylunachat_enhance_birth_chart_with_moon_phase($original_prompt, $birth_date, $birth_location = '') {
    $moon_phase = mylunachat_get_moon_phase($birth_date, $birth_location);

    $moon_enhancement = "\n\nIMPORTANT LUNAR CONTEXT: This person was born during a {$moon_phase['phase']} {$moon_phase['emoji']} " .
                       "({$moon_phase['illumination']}% illuminated). {$moon_phase['description']} " .
                       "Astrologically, this suggests: {$moon_phase['influence']} " .
                       "Please incorporate this lunar influence into your birth chart interpretation, " .
                       "explaining how this moon phase at birth affects their personality, emotional nature, and life path.";

    return $original_prompt . $moon_enhancement;
}

/**
 * ========================================
 * 📈 UTM TRACKING SYSTEM
 * ========================================
 */

/**
 * Generate UTM-enhanced link for conversion tracking
 *
 * @param string $base_url Base URL (e.g., '/pricing/')
 * @param string $source UTM source (e.g., 'mylunachat')
 * @param string $medium UTM medium (e.g., 'widget', 'modal', 'dashboard')
 * @param string $campaign UTM campaign (e.g., 'premium_upgrade', 'limit_reached')
 * @param array $additional_params Additional UTM parameters
 * @return string Enhanced URL with UTM parameters
 */
function mylunachat_generate_utm_link($base_url, $source = 'mylunachat', $medium = 'plugin', $campaign = 'premium_upgrade', $additional_params = array()) {
    // Ensure base URL starts with /
    if (strpos($base_url, 'http') !== 0 && strpos($base_url, '/') !== 0) {
        $base_url = '/' . $base_url;
    }

    // Convert relative URLs to absolute
    if (strpos($base_url, '/') === 0) {
        $base_url = home_url($base_url);
    }

    // Build UTM parameters
    $utm_params = array(
        'utm_source' => $source,
        'utm_medium' => $medium,
        'utm_campaign' => $campaign,
        'utm_content' => mylunachat_get_utm_content_context(),
        'utm_term' => mylunachat_get_current_page_context()
    );

    // Add additional parameters
    $utm_params = array_merge($utm_params, $additional_params);

    // Remove empty parameters
    $utm_params = array_filter($utm_params);

    // Build query string
    $query_string = http_build_query($utm_params);

    // Add query string to URL
    $separator = (strpos($base_url, '?') !== false) ? '&' : '?';
    $enhanced_url = $base_url . $separator . $query_string;

    // Track UTM generation for analytics
    mylunachat_track_utm_generation($utm_params);

    return $enhanced_url;
}

/**
 * Get UTM content context based on current user state
 *
 * @return string UTM content parameter
 */
function mylunachat_get_utm_content_context() {
    $user_id = get_current_user_id();

    if (!$user_id) {
        return 'anonymous_user';
    }

    if (mylunachat_is_premium_user($user_id)) {
        return 'premium_user'; // Shouldn't normally see upgrade links
    }

    $usage = mylunachat_get_daily_ai_usage($user_id);
    $limit = defined('MYLUNACHAT_FREE_AI_LIMIT') ? MYLUNACHAT_FREE_AI_LIMIT : 3;

    if ($usage >= $limit) {
        return 'limit_reached';
    } elseif ($usage >= ($limit - 1)) {
        return 'near_limit';
    } else {
        return 'within_limit';
    }
}

/**
 * Get current page context for UTM term
 *
 * @return string Page context
 */
function mylunachat_get_current_page_context() {
    global $post;

    if (is_home() || is_front_page()) {
        return 'homepage';
    } elseif (is_page()) {
        return 'page_' . $post->post_name;
    } elseif (is_single()) {
        return 'post_' . $post->post_type;
    } elseif (is_category()) {
        return 'category';
    } elseif (is_tag()) {
        return 'tag';
    } else {
        return 'other';
    }
}

/**
 * Track UTM generation for analytics
 *
 * @param array $utm_params UTM parameters
 */
function mylunachat_track_utm_generation($utm_params) {
    $user_id = get_current_user_id();

    if (!$user_id) {
        return; // Don't track anonymous users
    }

    $utm_history = get_user_meta($user_id, 'mylunachat_utm_history', true);
    if (!is_array($utm_history)) {
        $utm_history = array();
    }

    $utm_history[] = array(
        'utm_params' => $utm_params,
        'timestamp' => current_time('mysql'),
        'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '',
        'ip_address' => mylunachat_get_user_ip(),
        'page_url' => isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : ''
    );

    // Keep only last 100 UTM generations
    if (count($utm_history) > 100) {
        $utm_history = array_slice($utm_history, -100);
    }

    update_user_meta($user_id, 'mylunachat_utm_history', $utm_history);
}

/**
 * Get user IP address safely
 *
 * @return string IP address
 */
function mylunachat_get_user_ip() {
    $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');

    foreach ($ip_keys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }

    return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown';
}

/**
 * Store UTM data in session for conversion attribution
 *
 * @param array $utm_params UTM parameters from URL
 */
function mylunachat_store_utm_attribution($utm_params) {
    if (!session_id()) {
        session_start();
    }

    $_SESSION['mylunachat_utm_attribution'] = array(
        'utm_params' => $utm_params,
        'landing_time' => current_time('mysql'),
        'landing_page' => isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : ''
    );

    // Also store in user meta if logged in
    $user_id = get_current_user_id();
    if ($user_id) {
        update_user_meta($user_id, 'mylunachat_last_utm_attribution', $_SESSION['mylunachat_utm_attribution']);
    }
}

/**
 * Get stored UTM attribution data
 *
 * @return array|null UTM attribution data
 */
function mylunachat_get_utm_attribution() {
    if (!session_id()) {
        session_start();
    }

    if (isset($_SESSION['mylunachat_utm_attribution'])) {
        return $_SESSION['mylunachat_utm_attribution'];
    }

    // Fallback to user meta if logged in
    $user_id = get_current_user_id();
    if ($user_id) {
        return get_user_meta($user_id, 'mylunachat_last_utm_attribution', true);
    }

    return null;
}

/**
 * Enhanced upgrade link generation for widgets and CTAs
 *
 * @param string $context Where the link is being generated (widget, modal, dashboard, etc.)
 * @param string $campaign Specific campaign name
 * @return string UTM-enhanced upgrade URL
 */
function mylunachat_get_upgrade_link($context = 'widget', $campaign = 'premium_upgrade') {
    $base_url = '/pricing/'; // Adjust to your pricing page URL

    return mylunachat_generate_utm_link(
        $base_url,
        'mylunachat',
        $context,
        $campaign,
        array(
            'utm_user_id' => get_current_user_id(),
            'utm_timestamp' => time()
        )
    );
}

/**
 * ========================================
 * 🔹 5. UTILITY FUNCTIONS
 * ========================================
 */

/**
 * Get user's AI usage statistics
 *
 * @param int $user_id User ID
 * @return array Usage statistics
 */
function mylunachat_get_usage_stats($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }

    $is_premium = mylunachat_is_premium_user($user_id);
    $daily_usage = mylunachat_get_daily_ai_usage($user_id);
    $remaining = mylunachat_get_remaining_ai_uses($user_id);
    $free_limit = defined('MYLUNACHAT_FREE_AI_LIMIT') ? MYLUNACHAT_FREE_AI_LIMIT : 3;

    return array(
        'is_premium' => $is_premium,
        'daily_usage' => $daily_usage,
        'remaining_uses' => $remaining,
        'free_limit' => $free_limit,
        'percentage_used' => $is_premium ? 0 : ($daily_usage / $free_limit) * 100
    );
}

/**
 * Display usage widget shortcode
 *
 * Usage: [mylunachat_usage_widget]
 */
function mylunachat_usage_widget_shortcode($atts) {
    $atts = shortcode_atts(array(
        'show_upgrade' => 'true'
    ), $atts);

    $user_id = get_current_user_id();
    if (!$user_id) {
        return '<p>Please log in to view your usage.</p>';
    }

    $stats = mylunachat_get_usage_stats($user_id);

    ob_start();
    ?>
    <div class="mylunachat-usage-widget">
        <?php if ($stats['is_premium']): ?>
            <div class="usage-premium">
                <div class="premium-badge">
                    <span class="premium-icon">👑</span>
                    Premium Member
                </div>
                <p>Unlimited AI consultations</p>
            </div>
        <?php else: ?>
            <div class="usage-free">
                <div class="usage-header">
                    <h4>Daily AI Usage</h4>
                    <span class="usage-count"><?php echo $stats['daily_usage']; ?>/<?php echo $stats['free_limit']; ?></span>
                </div>

                <div class="usage-bar">
                    <div class="usage-progress" style="width: <?php echo min(100, $stats['percentage_used']); ?>%"></div>
                </div>

                <p class="usage-remaining">
                    <?php if ($stats['remaining_uses'] > 0): ?>
                        <?php echo $stats['remaining_uses']; ?> consultations remaining today
                    <?php else: ?>
                        Daily limit reached. Resets at midnight.
                    <?php endif; ?>
                </p>

                <?php if ($atts['show_upgrade'] === 'true'): ?>
                <div class="upgrade-prompt">
                    <a href="<?php echo esc_url(mylunachat_get_upgrade_link('usage_widget', 'widget_upgrade')); ?>" class="btn btn-premium">
                        Upgrade to Premium ✨
                    </a>
                </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
    <?php
    return ob_get_clean();
}

// Register usage widget shortcode
add_shortcode('mylunachat_usage_widget', 'mylunachat_usage_widget_shortcode');

/**
 * Admin settings page for core configuration
 */
function mylunachat_core_admin_menu() {
    add_submenu_page(
        'mylunachat-setup',
        'Core Settings',
        'Core Settings',
        'manage_options',
        'mylunachat-core-settings',
        'mylunachat_core_admin_page'
    );
}
add_action('admin_menu', 'mylunachat_core_admin_menu');

/**
 * Admin page content
 */
function mylunachat_core_admin_page() {
    if (isset($_POST['save_settings'])) {
        update_option('mylunachat_adsense_client', sanitize_text_field($_POST['adsense_client']));
        update_option('mylunachat_adsense_slot', sanitize_text_field($_POST['adsense_slot']));
        echo '<div class="notice notice-success"><p>Settings saved successfully!</p></div>';
    }

    $adsense_client = get_option('mylunachat_adsense_client', '');
    $adsense_slot = get_option('mylunachat_adsense_slot', '');
    ?>
    <div class="wrap">
        <h1>🌙 MyLunaChat Core Settings</h1>
        <form method="post">
            <table class="form-table">
                <tr>
                    <th scope="row">AdSense Client ID</th>
                    <td>
                        <input type="text" name="adsense_client" value="<?php echo esc_attr($adsense_client); ?>" class="regular-text" placeholder="ca-pub-XXXXXXXXXX" />
                        <p class="description">Your Google AdSense client ID</p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">AdSense Ad Slot</th>
                    <td>
                        <input type="text" name="adsense_slot" value="<?php echo esc_attr($adsense_slot); ?>" class="regular-text" placeholder="XXXXXXXXXX" />
                        <p class="description">Your Google AdSense ad slot ID</p>
                    </td>
                </tr>
            </table>
            <?php submit_button('Save Settings', 'primary', 'save_settings'); ?>
        </form>

        <h2>Usage Statistics</h2>
        <div class="mylunachat-admin-stats">
            <?php
            // Show some basic stats
            global $wpdb;
            $total_users = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->users}");
            $today = date('Y-m-d');
            $active_today = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(DISTINCT user_id) FROM {$wpdb->usermeta} WHERE meta_key LIKE %s",
                'mylunachat_ai_usage_' . $today
            ));
            ?>
            <p><strong>Total Users:</strong> <?php echo $total_users; ?></p>
            <p><strong>Active Today:</strong> <?php echo $active_today; ?></p>
        </div>
    </div>
    <?php
}
