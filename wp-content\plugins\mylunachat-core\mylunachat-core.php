<?php
/**
 * Plugin Name: MyLunaChat Core Functions
 * Plugin URI: https://mylunachat.com
 * Description: Core modular functions and shortcodes for MyLunaChat.com astrology platform
 * Version: 1.0.0
 * Author: MyLunaChat Development Team
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('MYLUNACHAT_CORE_VERSION', '1.0.0');
define('MYLUNACHAT_CORE_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('MYLUNACHAT_CORE_PLUGIN_URL', plugin_dir_url(__FILE__));

/**
 * ========================================
 * 🔹 1. AI USAGE ENFORCEMENT FUNCTIONS
 * ========================================
 */

/**
 * Check if user can use AI features
 * Returns true if user is premium OR has used less than 3 free AI interactions today
 * 
 * @param int $user_id User ID (optional, defaults to current user)
 * @return bool
 */
function mylunachat_can_use_ai($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if (!$user_id) {
        return false; // Not logged in
    }
    
    // Premium users have unlimited access
    if (mylunachat_is_premium_user($user_id)) {
        return true;
    }
    
    // Free users have daily limit
    $daily_usage = mylunachat_get_daily_ai_usage($user_id);
    $free_limit = defined('MYLUNACHAT_FREE_AI_LIMIT') ? MYLUNACHAT_FREE_AI_LIMIT : 3;
    
    return $daily_usage < $free_limit;
}

/**
 * Get user's daily AI usage count
 * Uses date-based key for automatic daily reset
 * 
 * @param int $user_id User ID (optional, defaults to current user)
 * @return int Usage count for today
 */
function mylunachat_get_daily_ai_usage($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if (!$user_id) {
        return 0;
    }
    
    $today = date('Y-m-d');
    $usage_key = 'mylunachat_ai_usage_' . $today;
    
    return (int) get_user_meta($user_id, $usage_key, true);
}

/**
 * Increment user's daily AI usage
 * Automatically creates new counter for new days
 * 
 * @param int $user_id User ID (optional, defaults to current user)
 * @return bool Success status
 */
function mylunachat_increment_ai_usage($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if (!$user_id) {
        return false;
    }
    
    $today = date('Y-m-d');
    $usage_key = 'mylunachat_ai_usage_' . $today;
    $current_usage = mylunachat_get_daily_ai_usage($user_id);
    
    // Increment usage
    $new_usage = $current_usage + 1;
    update_user_meta($user_id, $usage_key, $new_usage);
    
    // Clean up old usage data (keep only last 7 days)
    mylunachat_cleanup_old_usage_data($user_id);
    
    return true;
}

/**
 * Clean up old AI usage data (keep only last 7 days)
 * 
 * @param int $user_id User ID
 */
function mylunachat_cleanup_old_usage_data($user_id) {
    global $wpdb;
    
    // Get all usage meta keys for this user
    $meta_keys = $wpdb->get_col($wpdb->prepare(
        "SELECT meta_key FROM {$wpdb->usermeta} 
         WHERE user_id = %d 
         AND meta_key LIKE 'mylunachat_ai_usage_%%'",
        $user_id
    ));
    
    $cutoff_date = date('Y-m-d', strtotime('-7 days'));
    
    foreach ($meta_keys as $meta_key) {
        // Extract date from key
        $date_part = str_replace('mylunachat_ai_usage_', '', $meta_key);
        
        // Delete if older than 7 days
        if ($date_part < $cutoff_date) {
            delete_user_meta($user_id, $meta_key);
        }
    }
}

/**
 * Get remaining AI uses for today
 * 
 * @param int $user_id User ID (optional, defaults to current user)
 * @return int|string Remaining uses or "unlimited" for premium users
 */
function mylunachat_get_remaining_ai_uses($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if (mylunachat_is_premium_user($user_id)) {
        return 'unlimited';
    }
    
    $free_limit = defined('MYLUNACHAT_FREE_AI_LIMIT') ? MYLUNACHAT_FREE_AI_LIMIT : 3;
    $used = mylunachat_get_daily_ai_usage($user_id);
    
    return max(0, $free_limit - $used);
}

/**
 * Check if user is premium member
 * This function should already exist, but including for completeness
 * 
 * @param int $user_id User ID (optional, defaults to current user)
 * @return bool
 */
if (!function_exists('mylunachat_is_premium_user')) {
    function mylunachat_is_premium_user($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        if (!$user_id) {
            return false;
        }
        
        // Check MemberPress membership status
        if (function_exists('mepr_get_user_active_memberships')) {
            $memberships = mepr_get_user_active_memberships($user_id);
            return !empty($memberships);
        }
        
        // Fallback: check custom meta
        return get_user_meta($user_id, 'mylunachat_premium', true) === 'active';
    }
}

/**
 * ========================================
 * 🔹 2. AI WRAPPER CLASS
 * ========================================
 */

/**
 * MyLunaChat AI Wrapper Class
 * Handles all AI interactions with context and usage tracking
 */
class MylunaChatAI {
    
    private static $instance = null;
    private $api_key;
    private $model;
    private $temperature;
    
    /**
     * Singleton pattern
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->api_key = get_option('mylunachat_ai_api_key', '');
        $this->model = get_option('mylunachat_ai_model', 'gpt-3.5-turbo');
        $this->temperature = get_option('mylunachat_ai_temperature', 0.7);
    }
    
    /**
     * Get AI response with context and usage tracking
     * 
     * @param string $prompt User's prompt/question
     * @param string $context Context type (birth_chart, dream_interpreter, compatibility, etc.)
     * @param int $user_id User ID for usage tracking
     * @return array Response array with success status and data
     */
    public static function get_response($prompt, $context = 'general', $user_id = null) {
        $instance = self::getInstance();
        
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        // Check if user can use AI
        if (!mylunachat_can_use_ai($user_id)) {
            return array(
                'success' => false,
                'error' => 'daily_limit_reached',
                'message' => 'Daily AI limit reached. Upgrade to Premium for unlimited access.',
                'remaining_uses' => 0
            );
        }
        
        // Validate API key
        if (empty($instance->api_key)) {
            return array(
                'success' => false,
                'error' => 'api_not_configured',
                'message' => 'AI service is not configured. Please contact support.'
            );
        }
        
        // Get context-specific system prompt
        $system_prompt = $instance->get_system_prompt($context);
        
        // Make API call
        $response = $instance->call_openai_api($prompt, $system_prompt);
        
        if ($response['success']) {
            // Increment usage for successful calls
            mylunachat_increment_ai_usage($user_id);
            
            // Save conversation history
            $instance->save_conversation($user_id, $prompt, $response['data'], $context);
            
            // Add remaining uses to response
            $response['remaining_uses'] = mylunachat_get_remaining_ai_uses($user_id);
        }
        
        return $response;
    }
    
    /**
     * Get system prompt based on context
     * 
     * @param string $context Context type
     * @return string System prompt
     */
    private function get_system_prompt($context) {
        $prompts = array(
            'birth_chart' => "You are a professional astrologer with deep knowledge of natal chart interpretation. Analyze birth charts with insights about Sun, Moon, Rising signs, planetary positions, house placements, and major aspects. Use a warm, encouraging, and mystical tone while providing practical guidance.",
            
            'dream_interpreter' => "You are a Jungian dream analyst and spiritual guide with expertise in dream symbolism, archetypes, and astrological connections. Explore symbolic meanings, emotional context, connections to waking life, and provide spiritual and psychological insights with compassion.",
            
            'compatibility' => "You are an expert relationship astrologer specializing in synastry and compatibility analysis. Compare sun, moon, rising signs, analyze Venus and Mars placements, look at communication styles, and provide balanced relationship advice highlighting both harmonious aspects and growth areas.",
            
            'horoscope' => "You are a skilled astrologer creating personalized horoscopes. Provide general energy themes, love and relationship insights, career and financial guidance, health advice, spiritual growth opportunities, and lucky elements. Keep the tone uplifting and empowering.",
            
            'general' => "You are an intuitive AI astrologer and spiritual guide with vast astrological knowledge. Provide personalized insights that are warm, compassionate, spiritually insightful yet practical, encouraging, and based on astrological principles."
        );
        
        return isset($prompts[$context]) ? $prompts[$context] : $prompts['general'];
    }

    /**
     * Call OpenAI API
     *
     * @param string $prompt User prompt
     * @param string $system_prompt System prompt
     * @return array Response array
     */
    private function call_openai_api($prompt, $system_prompt) {
        $data = array(
            'model' => $this->model,
            'messages' => array(
                array(
                    'role' => 'system',
                    'content' => $system_prompt
                ),
                array(
                    'role' => 'user',
                    'content' => $prompt
                )
            ),
            'temperature' => $this->temperature,
            'max_tokens' => 1000
        );

        $response = wp_remote_post('https://api.openai.com/v1/chat/completions', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $this->api_key,
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode($data),
            'timeout' => 30
        ));

        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'error' => 'api_error',
                'message' => 'Unable to connect to AI service. Please try again.'
            );
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (isset($data['choices'][0]['message']['content'])) {
            return array(
                'success' => true,
                'data' => trim($data['choices'][0]['message']['content']),
                'usage' => isset($data['usage']) ? $data['usage'] : null
            );
        }

        return array(
            'success' => false,
            'error' => 'invalid_response',
            'message' => 'Invalid response from AI service.'
        );
    }

    /**
     * Save conversation history
     *
     * @param int $user_id User ID
     * @param string $prompt User prompt
     * @param string $response AI response
     * @param string $context Context type
     */
    private function save_conversation($user_id, $prompt, $response, $context) {
        $conversation_data = array(
            'user_id' => $user_id,
            'prompt' => $prompt,
            'response' => $response,
            'context' => $context,
            'timestamp' => current_time('mysql'),
            'date' => date('Y-m-d')
        );

        // Get existing conversations
        $conversations = get_user_meta($user_id, 'mylunachat_ai_conversations', true);
        if (!is_array($conversations)) {
            $conversations = array();
        }

        // Add new conversation
        $conversations[] = $conversation_data;

        // Keep only last 50 conversations
        if (count($conversations) > 50) {
            $conversations = array_slice($conversations, -50);
        }

        update_user_meta($user_id, 'mylunachat_ai_conversations', $conversations);
    }

    /**
     * Get user's conversation history
     *
     * @param int $user_id User ID
     * @param int $limit Number of conversations to return
     * @return array Conversation history
     */
    public static function get_conversation_history($user_id = null, $limit = 10) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        $conversations = get_user_meta($user_id, 'mylunachat_ai_conversations', true);
        if (!is_array($conversations)) {
            return array();
        }

        // Return most recent conversations
        return array_slice(array_reverse($conversations), 0, $limit);
    }
}

/**
 * ========================================
 * 🔹 3. REWARDED AD SHORTCODE SYSTEM
 * ========================================
 */

/**
 * Rewarded AI Shortcode
 * Shows AdSense ad for free users, premium users skip directly to content
 *
 * Usage: [mylunachat_rewarded_ai feature="dream_interpreter" delay="15"]
 *
 * @param array $atts Shortcode attributes
 * @return string HTML output
 */
function mylunachat_rewarded_ai_shortcode($atts) {
    $atts = shortcode_atts(array(
        'feature' => 'dream_interpreter',
        'delay' => '15',
        'ad_client' => get_option('mylunachat_adsense_client', 'ca-pub-XXXXXXXXXX'),
        'ad_slot' => get_option('mylunachat_adsense_slot', 'XXXXXXXXXX'),
        'premium_message' => 'Welcome back, Premium Member! 🌟'
    ), $atts);

    $user_id = get_current_user_id();
    $is_premium = mylunachat_is_premium_user($user_id);

    // Generate unique ID for this instance
    $instance_id = 'mylunachat_rewarded_' . uniqid();

    ob_start();
    ?>
    <div id="<?php echo esc_attr($instance_id); ?>" class="mylunachat-rewarded-container">

        <?php if ($is_premium): ?>
            <!-- Premium User - Skip Ad -->
            <div class="mylunachat-premium-welcome">
                <div class="premium-badge">
                    <span class="premium-icon">👑</span>
                    <?php echo esc_html($atts['premium_message']); ?>
                </div>
            </div>
            <div class="mylunachat-feature-content">
                <?php echo mylunachat_render_feature_content($atts['feature']); ?>
            </div>

        <?php else: ?>
            <!-- Free User - Show Rewarded Ad -->
            <div class="mylunachat-ad-container" id="<?php echo esc_attr($instance_id); ?>_ad">
                <div class="ad-header">
                    <h3>🌙 Support MyLunaChat</h3>
                    <p>Watch this ad to unlock your cosmic insights! ✨</p>
                    <div class="ad-timer">
                        <span id="<?php echo esc_attr($instance_id); ?>_timer"><?php echo esc_attr($atts['delay']); ?></span> seconds remaining
                    </div>
                </div>

                <!-- AdSense Ad -->
                <div class="adsense-container">
                    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=<?php echo esc_attr($atts['ad_client']); ?>" crossorigin="anonymous"></script>
                    <ins class="adsbygoogle"
                         style="display:block"
                         data-ad-client="<?php echo esc_attr($atts['ad_client']); ?>"
                         data-ad-slot="<?php echo esc_attr($atts['ad_slot']); ?>"
                         data-ad-format="auto"
                         data-full-width-responsive="true"></ins>
                    <script>
                         (adsbygoogle = window.adsbygoogle || []).push({});
                    </script>
                </div>

                <!-- Continue Button (hidden initially) -->
                <div class="continue-section" id="<?php echo esc_attr($instance_id); ?>_continue" style="display: none;">
                    <button class="btn btn-primary mylunachat-continue-btn" onclick="mylunachatShowFeature('<?php echo esc_attr($instance_id); ?>', '<?php echo esc_attr($atts['feature']); ?>')">
                        Continue to Your Reading ✨
                    </button>
                </div>
            </div>

            <!-- Feature Content (hidden initially) -->
            <div class="mylunachat-feature-content" id="<?php echo esc_attr($instance_id); ?>_content" style="display: none;">
                <div class="loading-message">
                    <div class="cosmic-loader">🌙 ✨ 🌟</div>
                    <p>Preparing your cosmic insights...</p>
                </div>
            </div>

        <?php endif; ?>

    </div>

    <?php if (!$is_premium): ?>
    <script>
    // Timer countdown
    let <?php echo esc_attr($instance_id); ?>_timeLeft = <?php echo intval($atts['delay']); ?>;
    let <?php echo esc_attr($instance_id); ?>_timer = setInterval(function() {
        <?php echo esc_attr($instance_id); ?>_timeLeft--;
        document.getElementById('<?php echo esc_attr($instance_id); ?>_timer').textContent = <?php echo esc_attr($instance_id); ?>_timeLeft;

        if (<?php echo esc_attr($instance_id); ?>_timeLeft <= 0) {
            clearInterval(<?php echo esc_attr($instance_id); ?>_timer);
            document.getElementById('<?php echo esc_attr($instance_id); ?>_continue').style.display = 'block';
            document.querySelector('#<?php echo esc_attr($instance_id); ?> .ad-timer').innerHTML = '<span class="ready-indicator">✅ Ready!</span>';
        }
    }, 1000);
    </script>
    <?php endif; ?>

    <?php
    return ob_get_clean();
}

/**
 * Render feature content based on feature type
 *
 * @param string $feature Feature type
 * @return string HTML content
 */
function mylunachat_render_feature_content($feature) {
    switch ($feature) {
        case 'dream_interpreter':
            return do_shortcode('[mylunachat_dream_interpreter]');

        case 'birth_chart':
            return do_shortcode('[mylunachat_birth_chart]');

        case 'compatibility':
            return do_shortcode('[mylunachat_compatibility]');

        case 'ai_chat':
            return do_shortcode('[mylunachat_ai_chat]');

        case 'horoscope':
            return do_shortcode('[mylunachat_horoscope type="daily"]');

        default:
            return '<p>Feature not available. Please contact support.</p>';
    }
}

/**
 * AJAX handler for loading feature content
 */
function mylunachat_ajax_load_feature() {
    check_ajax_referer('mylunachat_nonce', 'nonce');

    $feature = sanitize_text_field($_POST['feature']);
    $user_id = get_current_user_id();

    if (!$user_id) {
        wp_send_json_error('Please log in to continue.');
    }

    // Check if user can access this feature
    if (!mylunachat_can_use_ai($user_id) && !mylunachat_is_premium_user($user_id)) {
        wp_send_json_error('Daily limit reached. Please upgrade to Premium.');
    }

    $content = mylunachat_render_feature_content($feature);

    wp_send_json_success(array(
        'content' => $content,
        'remaining_uses' => mylunachat_get_remaining_ai_uses($user_id)
    ));
}

/**
 * ========================================
 * 🔹 4. SHORTCODE REGISTRATIONS & HOOKS
 * ========================================
 */

// Register shortcodes
add_shortcode('mylunachat_rewarded_ai', 'mylunachat_rewarded_ai_shortcode');

// Register AJAX handlers
add_action('wp_ajax_mylunachat_load_feature', 'mylunachat_ajax_load_feature');
add_action('wp_ajax_nopriv_mylunachat_load_feature', 'mylunachat_ajax_load_feature');

/**
 * Enqueue scripts and styles
 */
function mylunachat_core_enqueue_scripts() {
    wp_enqueue_script('mylunachat-core', MYLUNACHAT_CORE_PLUGIN_URL . 'js/mylunachat-core.js', array('jquery'), MYLUNACHAT_CORE_VERSION, true);
    wp_enqueue_style('mylunachat-core', MYLUNACHAT_CORE_PLUGIN_URL . 'css/mylunachat-core.css', array(), MYLUNACHAT_CORE_VERSION);

    wp_localize_script('mylunachat-core', 'mylunachat_core_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('mylunachat_nonce')
    ));
}
add_action('wp_enqueue_scripts', 'mylunachat_core_enqueue_scripts');

/**
 * ========================================
 * 🔹 5. UTILITY FUNCTIONS
 * ========================================
 */

/**
 * Get user's AI usage statistics
 *
 * @param int $user_id User ID
 * @return array Usage statistics
 */
function mylunachat_get_usage_stats($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }

    $is_premium = mylunachat_is_premium_user($user_id);
    $daily_usage = mylunachat_get_daily_ai_usage($user_id);
    $remaining = mylunachat_get_remaining_ai_uses($user_id);
    $free_limit = defined('MYLUNACHAT_FREE_AI_LIMIT') ? MYLUNACHAT_FREE_AI_LIMIT : 3;

    return array(
        'is_premium' => $is_premium,
        'daily_usage' => $daily_usage,
        'remaining_uses' => $remaining,
        'free_limit' => $free_limit,
        'percentage_used' => $is_premium ? 0 : ($daily_usage / $free_limit) * 100
    );
}

/**
 * Display usage widget shortcode
 *
 * Usage: [mylunachat_usage_widget]
 */
function mylunachat_usage_widget_shortcode($atts) {
    $atts = shortcode_atts(array(
        'show_upgrade' => 'true'
    ), $atts);

    $user_id = get_current_user_id();
    if (!$user_id) {
        return '<p>Please log in to view your usage.</p>';
    }

    $stats = mylunachat_get_usage_stats($user_id);

    ob_start();
    ?>
    <div class="mylunachat-usage-widget">
        <?php if ($stats['is_premium']): ?>
            <div class="usage-premium">
                <div class="premium-badge">
                    <span class="premium-icon">👑</span>
                    Premium Member
                </div>
                <p>Unlimited AI consultations</p>
            </div>
        <?php else: ?>
            <div class="usage-free">
                <div class="usage-header">
                    <h4>Daily AI Usage</h4>
                    <span class="usage-count"><?php echo $stats['daily_usage']; ?>/<?php echo $stats['free_limit']; ?></span>
                </div>

                <div class="usage-bar">
                    <div class="usage-progress" style="width: <?php echo min(100, $stats['percentage_used']); ?>%"></div>
                </div>

                <p class="usage-remaining">
                    <?php if ($stats['remaining_uses'] > 0): ?>
                        <?php echo $stats['remaining_uses']; ?> consultations remaining today
                    <?php else: ?>
                        Daily limit reached. Resets at midnight.
                    <?php endif; ?>
                </p>

                <?php if ($atts['show_upgrade'] === 'true'): ?>
                <div class="upgrade-prompt">
                    <a href="/pricing/" class="btn btn-premium">
                        Upgrade to Premium ✨
                    </a>
                </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
    <?php
    return ob_get_clean();
}

// Register usage widget shortcode
add_shortcode('mylunachat_usage_widget', 'mylunachat_usage_widget_shortcode');

/**
 * Admin settings page for core configuration
 */
function mylunachat_core_admin_menu() {
    add_submenu_page(
        'mylunachat-setup',
        'Core Settings',
        'Core Settings',
        'manage_options',
        'mylunachat-core-settings',
        'mylunachat_core_admin_page'
    );
}
add_action('admin_menu', 'mylunachat_core_admin_menu');

/**
 * Admin page content
 */
function mylunachat_core_admin_page() {
    if (isset($_POST['save_settings'])) {
        update_option('mylunachat_adsense_client', sanitize_text_field($_POST['adsense_client']));
        update_option('mylunachat_adsense_slot', sanitize_text_field($_POST['adsense_slot']));
        echo '<div class="notice notice-success"><p>Settings saved successfully!</p></div>';
    }

    $adsense_client = get_option('mylunachat_adsense_client', '');
    $adsense_slot = get_option('mylunachat_adsense_slot', '');
    ?>
    <div class="wrap">
        <h1>🌙 MyLunaChat Core Settings</h1>
        <form method="post">
            <table class="form-table">
                <tr>
                    <th scope="row">AdSense Client ID</th>
                    <td>
                        <input type="text" name="adsense_client" value="<?php echo esc_attr($adsense_client); ?>" class="regular-text" placeholder="ca-pub-XXXXXXXXXX" />
                        <p class="description">Your Google AdSense client ID</p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">AdSense Ad Slot</th>
                    <td>
                        <input type="text" name="adsense_slot" value="<?php echo esc_attr($adsense_slot); ?>" class="regular-text" placeholder="XXXXXXXXXX" />
                        <p class="description">Your Google AdSense ad slot ID</p>
                    </td>
                </tr>
            </table>
            <?php submit_button('Save Settings', 'primary', 'save_settings'); ?>
        </form>

        <h2>Usage Statistics</h2>
        <div class="mylunachat-admin-stats">
            <?php
            // Show some basic stats
            global $wpdb;
            $total_users = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->users}");
            $today = date('Y-m-d');
            $active_today = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(DISTINCT user_id) FROM {$wpdb->usermeta} WHERE meta_key LIKE %s",
                'mylunachat_ai_usage_' . $today
            ));
            ?>
            <p><strong>Total Users:</strong> <?php echo $total_users; ?></p>
            <p><strong>Active Today:</strong> <?php echo $active_today; ?></p>
        </div>
    </div>
    <?php
}
