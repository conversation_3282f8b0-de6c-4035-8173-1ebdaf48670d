# 🌟 StarPath - Astrology & AI Insight Platform

A comprehensive WordPress-based astrology platform that combines ancient wisdom with modern AI technology to provide personalized cosmic insights.

## ✨ Features

### 🔮 Core Astrology Features
- **Daily, Weekly & Monthly Horoscopes** - Personalized readings for all zodiac signs
- **Birth Chart Generator** - AI-powered natal chart analysis with detailed interpretations
- **Compatibility Readings** - Relationship insights based on astrological compatibility
- **Dream Interpreter** - Jungian dream analysis with astrological connections
- **Moon Calendar** - Track lunar phases and their influences
- **1-on-1 AI Astrologer** - Chat with AI for personalized guidance

### 💎 Premium Features
- Unlimited AI consultations
- Detailed PDF reports for birth charts and dream interpretations
- Ad-free experience
- User dashboard with saved readings
- Personalized email alerts for lunar events and transits
- Priority support

### 🎨 Design System
- **Colors**: Deep navy (#0A0F29), Gold accents (#FFD700), <PERSON>vender (#D6BBFB), Teal (#30D5C8)
- **Typography**: <PERSON><PERSON><PERSON> Decorative, Playfair Display, Lato, Open Sans
- **Celestial theme** with star animations and cosmic backgrounds

## 🚀 Installation

### Prerequisites
- WordPress 5.0 or higher
- PHP 7.4 or higher
- MySQL 5.6 or higher
- SSL certificate (recommended)

### Quick Setup

1. **Extract WordPress files** (already done in this setup)
2. **Configure database** in `wp-config.php`:
   ```php
   define('DB_NAME', 'starpath_db');
   define('DB_USER', 'your_username');
   define('DB_PASSWORD', 'your_password');
   define('DB_HOST', 'localhost');
   ```

3. **Run WordPress installation** by visiting your domain
4. **Activate plugins** in the following order:
   - StarPath Setup & Configuration
   - StarPath AI Integration
   - Astra Theme
   - Elementor Pro
   - MemberPress
   - Other required plugins

5. **Run the setup wizard** from WordPress Admin → StarPath Setup

## 🔧 Configuration

### AI Integration
1. Get an OpenAI API key from https://platform.openai.com/
2. Go to WordPress Admin → StarPath Setup → AI Settings
3. Enter your API key and configure model settings
4. Test the connection

### Membership Setup
1. Configure MemberPress with your payment processor
2. Create membership levels:
   - **Free**: 3 AI chats/day, ads enabled
   - **Premium ($10/month)**: Unlimited AI, PDF reports, no ads

### Google Services
1. **AdSense**: Configure through Ad Inserter plugin
2. **Analytics**: Set up via Google Site Kit
3. **Fonts**: Already configured to load Google Fonts

## 📄 Page Structure

| Page | URL | Access Level |
|------|-----|-------------|
| Home | `/` | Free |
| Daily Horoscope | `/horoscope/daily/` | Free (with ads) |
| Weekly Horoscope | `/horoscope/weekly/` | Free |
| Monthly Horoscope | `/horoscope/monthly/` | Free |
| Birth Chart | `/birth-chart/` | Free (basic) / Premium (PDF) |
| Compatibility | `/compatibility/` | Free (short) / Premium (detailed) |
| AI Reading | `/ai-reading/` | Free (limited) / Premium (unlimited) |
| Dream Interpreter | `/dream-interpreter/` | Free (1/day) / Premium (unlimited) |
| Moon Calendar | `/moon-calendar/` | Free |
| Learn | `/learn/` | Free |
| Blog | `/blog/` | Free |
| Dashboard | `/dashboard/` | Premium only |
| Login | `/login/` | All users |

## 🎯 Shortcodes

Use these shortcodes to embed features anywhere:

```php
[starpath_ai_chat height="400px"]           // AI chat interface
[starpath_birth_chart]                      // Birth chart form
[starpath_compatibility]                    // Compatibility form
[starpath_dream_interpreter]                // Dream interpretation form
[starpath_horoscope type="daily" sign="aries"] // Horoscope display
```

## 🔌 Custom Functions

### Check Premium Status
```php
if (starpath_is_premium_user()) {
    // Premium user content
}
```

### AI Usage Limits
```php
$can_use_ai = starpath_can_use_ai();
$daily_usage = starpath_get_daily_ai_usage();
```

### User Astrology Data
```php
$birth_date = get_user_meta($user_id, 'birth_date', true);
$zodiac_sign = get_user_meta($user_id, 'zodiac_sign', true);
```

## 🎨 Customization

### Theme Customization
- Child theme located in `/wp-content/themes/starpath-child/`
- Custom CSS variables in `style.css`
- JavaScript functionality in `js/starpath.js`

### Color Scheme
```css
:root {
    --starpath-navy: #0A0F29;
    --starpath-white: #F5F5F5;
    --starpath-gold: #FFD700;
    --starpath-lavender: #D6BBFB;
    --starpath-teal: #30D5C8;
}
```

## 📱 Mobile Responsiveness
- Fully responsive design
- Touch-friendly interface
- Optimized for all screen sizes
- Progressive Web App ready

## 🔒 Security & Privacy
- GDPR compliant
- SSL encryption
- Secure API communications
- User data protection
- Privacy policy included

## 📊 SEO Optimization
- Yoast SEO configured
- Structured data markup
- Optimized URLs
- Meta descriptions
- XML sitemap
- Google Analytics integration

## 🚀 Performance
- WP Rocket caching
- Image optimization
- Minified CSS/JS
- CDN ready
- Database optimization

## 🛠️ Development

### File Structure
```
wp-content/
├── themes/
│   └── starpath-child/
│       ├── style.css
│       ├── functions.php
│       ├── index.php
│       └── js/starpath.js
├── plugins/
│   ├── starpath-setup/
│   │   ├── starpath-setup.php
│   │   ├── css/setup.css
│   │   └── js/setup.js
│   └── starpath-ai/
│       └── starpath-ai.php
└── uploads/
    └── [generated PDFs]
```

### Custom Post Types
- `birth_chart` - Birth chart readings
- `ai_reading` - AI consultation history
- `dream_interpretation` - Dream analysis records

## 📞 Support

### Documentation
- Setup guide included
- API documentation
- Troubleshooting guide

### Contact
- Support email: <EMAIL>
- Documentation: docs.starpath.com
- Community forum: community.starpath.com

## 📝 License
GPL v2 or later

## 🔄 Updates
- Automatic WordPress updates enabled
- Plugin update notifications
- Theme update system
- Backup recommendations

---

**Ready to launch your cosmic journey? Follow the setup instructions above and let the stars guide your users! ✨**
