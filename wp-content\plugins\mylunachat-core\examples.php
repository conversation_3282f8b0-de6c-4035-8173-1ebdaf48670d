<?php
/**
 * MyLunaChat Core Plugin Examples
 * 
 * This file contains practical examples of how to use the MyLunaChat Core functions
 * Include this file in your theme or plugin to see the functions in action
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Example 1: Basic AI Usage Check
 */
function example_ai_usage_check() {
    $user_id = get_current_user_id();
    
    if (!$user_id) {
        return "Please log in to use AI features.";
    }
    
    if (mylunachat_can_use_ai($user_id)) {
        $remaining = mylunachat_get_remaining_ai_uses($user_id);
        if ($remaining === 'unlimited') {
            return "✨ Premium Member: Unlimited AI access!";
        } else {
            return "🌙 You have {$remaining} AI consultations remaining today.";
        }
    } else {
        return "🚫 Daily AI limit reached. <a href='/pricing/'>Upgrade to Premium</a> for unlimited access.";
    }
}

/**
 * Example 2: Custom AI Consultation Form
 */
function example_custom_ai_form() {
    ob_start();
    ?>
    <div class="custom-ai-consultation">
        <h3>🔮 Ask the Cosmic AI</h3>
        <form id="custom-ai-form" class="mylunachat-form">
            <div class="form-group">
                <label for="ai_question">Your Question:</label>
                <textarea id="ai_question" name="question" rows="4" placeholder="Ask about love, career, spirituality, or any life guidance..." required></textarea>
            </div>
            
            <div class="form-group">
                <label for="ai_context">Reading Type:</label>
                <select id="ai_context" name="context" required>
                    <option value="">Select reading type...</option>
                    <option value="general">General Guidance</option>
                    <option value="birth_chart">Birth Chart Insights</option>
                    <option value="compatibility">Relationship Advice</option>
                    <option value="dream_interpreter">Dream Analysis</option>
                </select>
            </div>
            
            <div class="usage-info">
                <?php echo example_ai_usage_check(); ?>
            </div>
            
            <button type="submit" class="btn btn-primary">
                Consult the Stars ✨
            </button>
        </form>
        
        <div id="ai-response-container" style="display: none;">
            <!-- AI response will be loaded here -->
        </div>
    </div>
    
    <script>
    jQuery(document).ready(function($) {
        $('#custom-ai-form').on('submit', function(e) {
            e.preventDefault();
            
            const form = $(this);
            const question = $('#ai_question').val();
            const context = $('#ai_context').val();
            const submitBtn = form.find('button[type="submit"]');
            const responseContainer = $('#ai-response-container');
            
            // Disable form
            submitBtn.prop('disabled', true).text('Consulting the cosmos...');
            
            $.ajax({
                url: mylunachat_core_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'example_custom_ai_request',
                    question: question,
                    context: context,
                    nonce: mylunachat_core_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        responseContainer.html(`
                            <div class="ai-response">
                                <h4>✨ Your Cosmic Guidance</h4>
                                <div class="response-content">${response.data.message}</div>
                                <div class="usage-update">
                                    Remaining consultations: ${response.data.remaining_uses}
                                </div>
                            </div>
                        `).fadeIn();
                        
                        // Clear form
                        form[0].reset();
                        
                        // Update usage info
                        $('.usage-info').html(response.data.usage_message);
                    } else {
                        alert('Error: ' + response.data.message);
                    }
                },
                error: function() {
                    alert('Connection error. Please try again.');
                },
                complete: function() {
                    submitBtn.prop('disabled', false).text('Consult the Stars ✨');
                }
            });
        });
    });
    </script>
    <?php
    return ob_get_clean();
}

/**
 * AJAX handler for custom AI form
 */
function example_custom_ai_request() {
    check_ajax_referer('mylunachat_nonce', 'nonce');
    
    $question = sanitize_textarea_field($_POST['question']);
    $context = sanitize_text_field($_POST['context']);
    $user_id = get_current_user_id();
    
    if (!$user_id) {
        wp_send_json_error(array('message' => 'Please log in to continue.'));
    }
    
    // Use the AI wrapper class
    $response = MylunaChatAI::get_response($question, $context, $user_id);
    
    if ($response['success']) {
        wp_send_json_success(array(
            'message' => $response['data'],
            'remaining_uses' => $response['remaining_uses'],
            'usage_message' => example_ai_usage_check()
        ));
    } else {
        wp_send_json_error(array('message' => $response['message']));
    }
}
add_action('wp_ajax_example_custom_ai_request', 'example_custom_ai_request');
add_action('wp_ajax_nopriv_example_custom_ai_request', 'example_custom_ai_request');

/**
 * Example 3: Usage Statistics Dashboard
 */
function example_usage_dashboard() {
    $user_id = get_current_user_id();
    
    if (!$user_id) {
        return '<p>Please log in to view your usage statistics.</p>';
    }
    
    $stats = mylunachat_get_usage_stats($user_id);
    $history = MylunaChatAI::get_conversation_history($user_id, 5);
    
    ob_start();
    ?>
    <div class="usage-dashboard">
        <h3>📊 Your MyLunaChat Usage</h3>
        
        <div class="stats-grid">
            <div class="stat-card">
                <h4>Account Type</h4>
                <p class="stat-value">
                    <?php echo $stats['is_premium'] ? '👑 Premium' : '🌙 Free'; ?>
                </p>
            </div>
            
            <?php if (!$stats['is_premium']): ?>
            <div class="stat-card">
                <h4>Today's Usage</h4>
                <p class="stat-value">
                    <?php echo $stats['daily_usage']; ?>/<?php echo $stats['free_limit']; ?>
                </p>
                <div class="usage-bar">
                    <div class="usage-progress" style="width: <?php echo min(100, $stats['percentage_used']); ?>%"></div>
                </div>
            </div>
            
            <div class="stat-card">
                <h4>Remaining Today</h4>
                <p class="stat-value">
                    <?php echo $stats['remaining_uses']; ?> consultations
                </p>
            </div>
            <?php endif; ?>
        </div>
        
        <?php if (!empty($history)): ?>
        <div class="recent-conversations">
            <h4>🌟 Recent Consultations</h4>
            <?php foreach ($history as $conversation): ?>
            <div class="conversation-item">
                <div class="conversation-meta">
                    <span class="context-badge"><?php echo ucfirst($conversation['context']); ?></span>
                    <span class="conversation-date"><?php echo date('M j, Y g:i A', strtotime($conversation['timestamp'])); ?></span>
                </div>
                <div class="conversation-preview">
                    <strong>Q:</strong> <?php echo wp_trim_words($conversation['prompt'], 15); ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
        
        <?php if (!$stats['is_premium']): ?>
        <div class="upgrade-section">
            <h4>🚀 Upgrade to Premium</h4>
            <ul class="premium-benefits">
                <li>✨ Unlimited AI consultations</li>
                <li>📊 Detailed PDF reports</li>
                <li>🚫 Ad-free experience</li>
                <li>🌙 Personalized alerts</li>
            </ul>
            <a href="/pricing/" class="btn btn-premium">Upgrade Now - $10/month</a>
        </div>
        <?php endif; ?>
    </div>
    
    <style>
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin: 20px 0;
    }
    
    .stat-card {
        background: rgba(245, 245, 245, 0.05);
        padding: 20px;
        border-radius: 12px;
        border: 1px solid rgba(214, 187, 251, 0.2);
        text-align: center;
    }
    
    .stat-value {
        font-size: 1.5em;
        font-weight: 600;
        color: #FFD700;
        margin: 10px 0;
    }
    
    .conversation-item {
        background: rgba(245, 245, 245, 0.03);
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 10px;
        border-left: 3px solid #30D5C8;
    }
    
    .conversation-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }
    
    .context-badge {
        background: rgba(214, 187, 251, 0.2);
        color: #D6BBFB;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8em;
        text-transform: uppercase;
    }
    
    .conversation-date {
        color: rgba(245, 245, 245, 0.6);
        font-size: 0.9em;
    }
    
    .premium-benefits {
        list-style: none;
        padding: 0;
        margin: 15px 0;
    }
    
    .premium-benefits li {
        padding: 5px 0;
        color: #D6BBFB;
    }
    </style>
    <?php
    return ob_get_clean();
}

/**
 * Example 4: Shortcode for testing rewarded ads
 */
function example_rewarded_ad_test() {
    return '
    <div class="rewarded-ad-examples">
        <h3>🎯 Rewarded Ad Examples</h3>
        
        <div class="example-section">
            <h4>Dream Interpreter (15 second delay)</h4>
            ' . do_shortcode('[mylunachat_rewarded_ai feature="dream_interpreter" delay="15"]') . '
        </div>
        
        <div class="example-section">
            <h4>Birth Chart (10 second delay)</h4>
            ' . do_shortcode('[mylunachat_rewarded_ai feature="birth_chart" delay="10"]') . '
        </div>
        
        <div class="example-section">
            <h4>Compatibility Reading (20 second delay)</h4>
            ' . do_shortcode('[mylunachat_rewarded_ai feature="compatibility" delay="20"]') . '
        </div>
    </div>';
}

/**
 * Register example shortcodes for testing
 */
add_shortcode('mylunachat_example_ai_form', 'example_custom_ai_form');
add_shortcode('mylunachat_example_dashboard', 'example_usage_dashboard');
add_shortcode('mylunachat_example_rewarded_ads', 'example_rewarded_ad_test');

/**
 * Example 5: Admin test page
 */
function example_admin_test_page() {
    add_submenu_page(
        'mylunachat-setup',
        'Core Examples',
        'Core Examples',
        'manage_options',
        'mylunachat-core-examples',
        'example_admin_test_page_content'
    );
}
add_action('admin_menu', 'example_admin_test_page');

function example_admin_test_page_content() {
    ?>
    <div class="wrap">
        <h1>🧪 MyLunaChat Core Examples</h1>
        
        <div class="example-section">
            <h2>Usage Check Function</h2>
            <code>mylunachat_can_use_ai()</code>
            <p><strong>Result:</strong> <?php echo example_ai_usage_check(); ?></p>
        </div>
        
        <div class="example-section">
            <h2>Usage Statistics</h2>
            <code>mylunachat_get_usage_stats()</code>
            <pre><?php print_r(mylunachat_get_usage_stats(1)); ?></pre>
        </div>
        
        <div class="example-section">
            <h2>Shortcode Examples</h2>
            <p>Use these shortcodes on any page or post:</p>
            <ul>
                <li><code>[mylunachat_example_ai_form]</code> - Custom AI consultation form</li>
                <li><code>[mylunachat_example_dashboard]</code> - User usage dashboard</li>
                <li><code>[mylunachat_example_rewarded_ads]</code> - Rewarded ad examples</li>
                <li><code>[mylunachat_usage_widget]</code> - Usage widget</li>
                <li><code>[mylunachat_rewarded_ai feature="dream_interpreter"]</code> - Rewarded ad</li>
            </ul>
        </div>
    </div>
    <?php
}
