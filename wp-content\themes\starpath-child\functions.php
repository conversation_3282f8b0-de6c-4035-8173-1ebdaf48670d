<?php
/**
 * StarPath Child Theme Functions
 * 
 * Custom functionality for the StarPath Astrology Platform
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Enqueue parent and child theme styles
 */
function starpath_enqueue_styles() {
    // Enqueue parent theme style
    wp_enqueue_style('astra-style', get_template_directory_uri() . '/style.css');
    
    // Enqueue child theme style
    wp_enqueue_style('starpath-child-style', 
        get_stylesheet_directory_uri() . '/style.css',
        array('astra-style'),
        wp_get_theme()->get('Version')
    );
    
    // Enqueue Google Fonts
    wp_enqueue_style('starpath-fonts', 
        'https://fonts.googleapis.com/css2?family=Cinzel+Decorative:wght@400;700&family=Playfair+Display:wght@400;500;600;700&family=Lato:wght@300;400;500;700&family=Open+Sans:wght@300;400;500;600;700&display=swap',
        array(),
        null
    );
}
add_action('wp_enqueue_scripts', 'starpath_enqueue_styles');

/**
 * Enqueue custom JavaScript
 */
function starpath_enqueue_scripts() {
    wp_enqueue_script('starpath-main', 
        get_stylesheet_directory_uri() . '/js/starpath.js',
        array('jquery'),
        wp_get_theme()->get('Version'),
        true
    );
    
    // Localize script for AJAX
    wp_localize_script('starpath-main', 'starpath_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('starpath_nonce'),
        'free_limit' => STARPATH_FREE_AI_LIMIT,
        'premium_price' => STARPATH_PREMIUM_PRICE
    ));
}
add_action('wp_enqueue_scripts', 'starpath_enqueue_scripts');

/**
 * Theme setup
 */
function starpath_theme_setup() {
    // Add theme support for various features
    add_theme_support('post-thumbnails');
    add_theme_support('custom-logo');
    add_theme_support('title-tag');
    add_theme_support('custom-background');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'starpath'),
        'footer' => __('Footer Menu', 'starpath'),
    ));
}
add_action('after_setup_theme', 'starpath_theme_setup');

/**
 * Register widget areas
 */
function starpath_widgets_init() {
    register_sidebar(array(
        'name'          => __('Sidebar', 'starpath'),
        'id'            => 'sidebar-1',
        'description'   => __('Add widgets here.', 'starpath'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h2 class="widget-title">',
        'after_title'   => '</h2>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer Widget Area', 'starpath'),
        'id'            => 'footer-1',
        'description'   => __('Add widgets here for the footer.', 'starpath'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
}
add_action('widgets_init', 'starpath_widgets_init');

/**
 * Custom post types for StarPath
 */
function starpath_register_post_types() {
    // Birth Charts
    register_post_type('birth_chart', array(
        'labels' => array(
            'name' => 'Birth Charts',
            'singular_name' => 'Birth Chart',
        ),
        'public' => false,
        'show_ui' => true,
        'show_in_menu' => true,
        'capability_type' => 'post',
        'supports' => array('title', 'editor', 'custom-fields'),
        'menu_icon' => 'dashicons-star-filled',
    ));
    
    // AI Readings
    register_post_type('ai_reading', array(
        'labels' => array(
            'name' => 'AI Readings',
            'singular_name' => 'AI Reading',
        ),
        'public' => false,
        'show_ui' => true,
        'show_in_menu' => true,
        'capability_type' => 'post',
        'supports' => array('title', 'editor', 'custom-fields'),
        'menu_icon' => 'dashicons-format-chat',
    ));
    
    // Dream Interpretations
    register_post_type('dream_interpretation', array(
        'labels' => array(
            'name' => 'Dream Interpretations',
            'singular_name' => 'Dream Interpretation',
        ),
        'public' => false,
        'show_ui' => true,
        'show_in_menu' => true,
        'capability_type' => 'post',
        'supports' => array('title', 'editor', 'custom-fields'),
        'menu_icon' => 'dashicons-cloud',
    ));
}
add_action('init', 'starpath_register_post_types');

/**
 * Custom user meta fields
 */
function starpath_add_user_meta_fields($user) {
    ?>
    <h3><?php _e('StarPath Profile Information', 'starpath'); ?></h3>
    <table class="form-table">
        <tr>
            <th><label for="birth_date"><?php _e('Birth Date', 'starpath'); ?></label></th>
            <td>
                <input type="date" name="birth_date" id="birth_date" 
                       value="<?php echo esc_attr(get_user_meta($user->ID, 'birth_date', true)); ?>" 
                       class="regular-text" />
            </td>
        </tr>
        <tr>
            <th><label for="birth_time"><?php _e('Birth Time', 'starpath'); ?></label></th>
            <td>
                <input type="time" name="birth_time" id="birth_time" 
                       value="<?php echo esc_attr(get_user_meta($user->ID, 'birth_time', true)); ?>" 
                       class="regular-text" />
            </td>
        </tr>
        <tr>
            <th><label for="birth_location"><?php _e('Birth Location', 'starpath'); ?></label></th>
            <td>
                <input type="text" name="birth_location" id="birth_location" 
                       value="<?php echo esc_attr(get_user_meta($user->ID, 'birth_location', true)); ?>" 
                       class="regular-text" placeholder="City, Country" />
            </td>
        </tr>
        <tr>
            <th><label for="zodiac_sign"><?php _e('Zodiac Sign', 'starpath'); ?></label></th>
            <td>
                <select name="zodiac_sign" id="zodiac_sign">
                    <option value=""><?php _e('Select Sign', 'starpath'); ?></option>
                    <?php
                    $signs = array(
                        'aries' => 'Aries', 'taurus' => 'Taurus', 'gemini' => 'Gemini',
                        'cancer' => 'Cancer', 'leo' => 'Leo', 'virgo' => 'Virgo',
                        'libra' => 'Libra', 'scorpio' => 'Scorpio', 'sagittarius' => 'Sagittarius',
                        'capricorn' => 'Capricorn', 'aquarius' => 'Aquarius', 'pisces' => 'Pisces'
                    );
                    $current_sign = get_user_meta($user->ID, 'zodiac_sign', true);
                    foreach ($signs as $value => $label) {
                        echo '<option value="' . $value . '"' . selected($current_sign, $value, false) . '>' . $label . '</option>';
                    }
                    ?>
                </select>
            </td>
        </tr>
    </table>
    <?php
}
add_action('show_user_profile', 'starpath_add_user_meta_fields');
add_action('edit_user_profile', 'starpath_add_user_meta_fields');

/**
 * Save custom user meta fields
 */
function starpath_save_user_meta_fields($user_id) {
    if (!current_user_can('edit_user', $user_id)) {
        return false;
    }
    
    $fields = array('birth_date', 'birth_time', 'birth_location', 'zodiac_sign');
    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            update_user_meta($user_id, $field, sanitize_text_field($_POST[$field]));
        }
    }
}
add_action('personal_options_update', 'starpath_save_user_meta_fields');
add_action('edit_user_profile_update', 'starpath_save_user_meta_fields');

/**
 * Check if user is premium member
 */
function starpath_is_premium_user($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if (!$user_id) {
        return false;
    }
    
    // Check MemberPress membership status
    if (function_exists('mepr_get_user_active_memberships')) {
        $memberships = mepr_get_user_active_memberships($user_id);
        return !empty($memberships);
    }
    
    // Fallback: check custom meta
    return get_user_meta($user_id, 'starpath_premium', true) === 'active';
}

/**
 * Get user's daily AI usage count
 */
function starpath_get_daily_ai_usage($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if (!$user_id) {
        return 0;
    }
    
    $today = date('Y-m-d');
    $usage_key = 'ai_usage_' . $today;
    
    return (int) get_user_meta($user_id, $usage_key, true);
}

/**
 * Increment user's daily AI usage
 */
function starpath_increment_ai_usage($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if (!$user_id) {
        return false;
    }
    
    $today = date('Y-m-d');
    $usage_key = 'ai_usage_' . $today;
    $current_usage = starpath_get_daily_ai_usage($user_id);
    
    update_user_meta($user_id, $usage_key, $current_usage + 1);
    return true;
}

/**
 * Check if user can use AI features
 */
function starpath_can_use_ai($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if (!$user_id) {
        return false;
    }
    
    // Premium users have unlimited access
    if (starpath_is_premium_user($user_id)) {
        return true;
    }
    
    // Free users have daily limit
    $daily_usage = starpath_get_daily_ai_usage($user_id);
    return $daily_usage < STARPATH_FREE_AI_LIMIT;
}

/**
 * Add custom body classes
 */
function starpath_body_classes($classes) {
    if (starpath_is_premium_user()) {
        $classes[] = 'premium-user';
    } else {
        $classes[] = 'free-user';
    }
    
    return $classes;
}
add_filter('body_class', 'starpath_body_classes');

/**
 * Custom excerpt length
 */
function starpath_excerpt_length($length) {
    return 25;
}
add_filter('excerpt_length', 'starpath_excerpt_length');

/**
 * Custom excerpt more text
 */
function starpath_excerpt_more($more) {
    return '... <a href="' . get_permalink() . '" class="read-more">Read More ✨</a>';
}
add_filter('excerpt_more', 'starpath_excerpt_more');
