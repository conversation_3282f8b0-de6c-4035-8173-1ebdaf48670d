/**
 * MyLunaChat Setup Plugin Styles
 */

.mylunachat-setup-wrap {
    max-width: 1200px;
    margin: 20px auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.mylunachat-setup-wrap h1 {
    background: linear-gradient(135deg, #0A0F29, #1a1f3a);
    color: #FFD700;
    margin: 0;
    padding: 30px;
    text-align: center;
    font-size: 2.5em;
    border-bottom: 3px solid #FFD700;
}

.mylunachat-setup-wrap p {
    text-align: center;
    font-size: 1.1em;
    color: #666;
    margin: 20px 0;
}

/* Progress Bar */
.mylunachat-setup-progress {
    padding: 20px 30px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #30D5C8, #D6BBFB);
    border-radius: 10px;
    transition: width 0.5s ease;
}

.progress-text {
    text-align: center;
    font-weight: 600;
    color: #0A0F29;
}

/* Setup Steps */
.mylunachat-setup-steps {
    padding: 30px;
}

.setup-step {
    margin-bottom: 40px;
    padding: 30px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    background: #fff;
    transition: all 0.3s ease;
}

.setup-step.active {
    border-color: #30D5C8;
    box-shadow: 0 5px 20px rgba(48, 213, 200, 0.2);
}

.setup-step h2 {
    color: #0A0F29;
    margin-bottom: 15px;
    font-size: 1.8em;
}

.setup-step p {
    text-align: left;
    margin-bottom: 25px;
}

/* Plugin List */
.plugin-list {
    margin: 20px 0;
}

.plugin-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    margin-bottom: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #e9ecef;
    transition: all 0.3s ease;
}

.plugin-item.installing {
    border-left-color: #30D5C8;
    background: rgba(48, 213, 200, 0.1);
}

.plugin-item.completed {
    border-left-color: #28a745;
    background: rgba(40, 167, 69, 0.1);
}

.plugin-item.error {
    border-left-color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
}

.plugin-name {
    font-weight: 600;
    color: #0A0F29;
}

.plugin-status {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.9em;
    font-weight: 500;
}

.plugin-status.pending {
    background: #ffc107;
    color: #856404;
}

.plugin-status.installing {
    background: #30D5C8;
    color: #fff;
}

.plugin-status.completed {
    background: #28a745;
    color: #fff;
}

.plugin-status.error {
    background: #dc3545;
    color: #fff;
}

.plugin-progress {
    width: 100px;
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
    margin-left: 15px;
}

.plugin-progress.active::after {
    content: '';
    display: block;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, #30D5C8, #D6BBFB);
    animation: progress-slide 2s infinite;
}

@keyframes progress-slide {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Pages List */
.pages-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.page-item {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: center;
    font-weight: 500;
    color: #0A0F29;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.page-item.creating {
    border-color: #30D5C8;
    background: rgba(48, 213, 200, 0.1);
}

.page-item.completed {
    border-color: #28a745;
    background: rgba(40, 167, 69, 0.1);
}

/* Settings List */
.settings-list {
    margin: 20px 0;
}

.setting-item {
    padding: 15px;
    margin-bottom: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #e9ecef;
    font-weight: 500;
    color: #0A0F29;
    transition: all 0.3s ease;
}

.setting-item.configuring {
    border-left-color: #30D5C8;
    background: rgba(48, 213, 200, 0.1);
}

.setting-item.completed {
    border-left-color: #28a745;
    background: rgba(40, 167, 69, 0.1);
}

/* Buttons */
.button.button-primary {
    background: linear-gradient(135deg, #30D5C8, #D6BBFB);
    border: none;
    color: #0A0F29;
    font-weight: 600;
    padding: 12px 30px;
    border-radius: 25px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.button.button-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(48, 213, 200, 0.3);
}

.button.button-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Completion Section */
.completion-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin: 30px 0;
    flex-wrap: wrap;
}

.completion-actions .button {
    padding: 15px 25px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.completion-actions .button-primary {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #0A0F29;
}

.completion-actions .button:not(.button-primary) {
    background: #f8f9fa;
    color: #0A0F29;
    border: 2px solid #e9ecef;
}

.completion-actions .button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.next-steps {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 8px;
    margin-top: 30px;
}

.next-steps h3 {
    color: #0A0F29;
    margin-bottom: 15px;
}

.next-steps ul {
    list-style: none;
    padding: 0;
}

.next-steps li {
    padding: 8px 0;
    padding-left: 25px;
    position: relative;
    color: #666;
}

.next-steps li::before {
    content: '✨';
    position: absolute;
    left: 0;
    top: 8px;
}

/* Footer */
.mylunachat-setup-footer {
    background: #0A0F29;
    color: #F5F5F5;
    padding: 20px 30px;
    text-align: center;
}

.mylunachat-setup-footer a {
    color: #FFD700;
    text-decoration: none;
}

.mylunachat-setup-footer a:hover {
    text-decoration: underline;
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #30D5C8;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .mylunachat-setup-wrap {
        margin: 10px;
        border-radius: 0;
    }

    .mylunachat-setup-wrap h1 {
        font-size: 2em;
        padding: 20px;
    }

    .mylunachat-setup-steps {
        padding: 20px;
    }
    
    .setup-step {
        padding: 20px;
    }
    
    .plugin-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .completion-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .completion-actions .button {
        width: 100%;
        max-width: 300px;
        text-align: center;
    }
}
